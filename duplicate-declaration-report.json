{"timestamp": "2025-07-11T16:38:56.094Z", "summary": {"totalClasses": 25, "duplicateClasses": 25, "cleanClasses": 0, "errors": 0}, "details": {"duplicateClasses": [{"className": "EventBus", "declarations": [{"file": "ui\\sidepanel\\core\\EventBus.js", "line": 7, "pattern": "class EventBus {", "type": "class"}, {"file": "ui\\sidepanel\\core\\EventBus.js", "line": 236, "pattern": "window.EventBus =", "type": "window"}], "count": 2}, {"className": "DebugLogger", "declarations": [{"file": "ui\\sidepanel\\utils\\DebugLogger.js", "line": 7, "pattern": "class DebugLogger {", "type": "class"}, {"file": "ui\\sidepanel\\utils\\DebugLogger.js", "line": 566, "pattern": "window.DebugLogger =", "type": "window"}], "count": 2}, {"className": "Date<PERSON><PERSON><PERSON><PERSON>", "declarations": [{"file": "ui\\sidepanel\\compatibility\\LegacyAdapter.js", "line": 144, "pattern": "window.DateFormatter =", "type": "window"}, {"file": "ui\\sidepanel\\utils\\DateFormatter.js", "line": 7, "pattern": "class DateFormatter {", "type": "class"}, {"file": "ui\\sidepanel\\utils\\DateFormatter.js", "line": 405, "pattern": "window.DateFormatter =", "type": "window"}], "count": 3}, {"className": "MessageHelper", "declarations": [{"file": "ui\\sidepanel\\utils\\MessageHelper.js", "line": 7, "pattern": "class MessageHelper {", "type": "class"}, {"file": "ui\\sidepanel\\utils\\MessageHelper.js", "line": 647, "pattern": "window.MessageHelper =", "type": "window"}], "count": 2}, {"className": "StorageService", "declarations": [{"file": "ui\\sidepanel\\data\\StorageService.js", "line": 7, "pattern": "class StorageService {", "type": "class"}, {"file": "ui\\sidepanel\\data\\StorageService.js", "line": 844, "pattern": "window.StorageService =", "type": "window"}], "count": 2}, {"className": "DataValidator", "declarations": [{"file": "ui\\sidepanel\\form\\DataValidator.js", "line": 7, "pattern": "class DataValidator {", "type": "class"}, {"file": "ui\\sidepanel\\form\\DataValidator.js", "line": 664, "pattern": "window.DataValidator =", "type": "window"}], "count": 2}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "declarations": [{"file": "ui\\sidepanel\\ui\\ModalManager.js", "line": 7, "pattern": "class ModalManager {", "type": "class"}, {"file": "ui\\sidepanel\\ui\\ModalManager.js", "line": 884, "pattern": "window.ModalManager =", "type": "window"}], "count": 2}, {"className": "DataManager", "declarations": [{"file": "ui\\sidepanel\\data\\DataManager.js", "line": 7, "pattern": "class DataManager {", "type": "class"}, {"file": "ui\\sidepanel\\data\\DataManager.js", "line": 873, "pattern": "window.DataManager =", "type": "window"}], "count": 2}, {"className": "FieldMatcher", "declarations": [{"file": "ui\\sidepanel\\form\\FieldMatcher.js", "line": 7, "pattern": "class FieldMatcher {", "type": "class"}, {"file": "ui\\sidepanel\\form\\FieldMatcher.js", "line": 731, "pattern": "window.FieldMatcher =", "type": "window"}], "count": 2}, {"className": "AIService", "declarations": [{"file": "ui\\sidepanel\\ai\\AIService.js", "line": 7, "pattern": "class AIService {", "type": "class"}, {"file": "ui\\sidepanel\\ai\\AIService.js", "line": 796, "pattern": "window.AIService =", "type": "window"}], "count": 2}, {"className": "<PERSON><PERSON><PERSON><PERSON>", "declarations": [{"file": "ui\\sidepanel\\ui\\UIRenderer.js", "line": 7, "pattern": "class UIRenderer {", "type": "class"}, {"file": "ui\\sidepanel\\ui\\UIRenderer.js", "line": 812, "pattern": "window.UIRenderer =", "type": "window"}], "count": 2}, {"className": "FormFiller", "declarations": [{"file": "ui\\sidepanel\\form\\FormFiller.js", "line": 7, "pattern": "class FormFiller {", "type": "class"}, {"file": "ui\\sidepanel\\form\\FormFiller.js", "line": 617, "pattern": "window.FormFiller =", "type": "window"}], "count": 2}, {"className": "CityViewer", "declarations": [{"file": "ui\\sidepanel\\features\\CityViewer.js", "line": 7, "pattern": "class CityViewer {", "type": "class"}, {"file": "ui\\sidepanel\\features\\CityViewer.js", "line": 847, "pattern": "window.CityViewer =", "type": "window"}], "count": 2}, {"className": "<PERSON><PERSON><PERSON><PERSON>", "declarations": [{"file": "ui\\sidepanel\\ai\\TextParser.js", "line": 7, "pattern": "class TextParser {", "type": "class"}, {"file": "ui\\sidepanel\\ai\\TextParser.js", "line": 582, "pattern": "window.TextParser =", "type": "window"}], "count": 2}, {"className": "ConfidenceEvaluator", "declarations": [{"file": "ui\\sidepanel\\compatibility\\LegacyAdapter.js", "line": 117, "pattern": "window.ConfidenceEvaluator =", "type": "window"}, {"file": "ui\\sidepanel\\features\\ConfidenceEvaluator.js", "line": 7, "pattern": "class ConfidenceEvaluator {", "type": "class"}, {"file": "ui\\sidepanel\\features\\ConfidenceEvaluator.js", "line": 873, "pattern": "window.ConfidenceEvaluator =", "type": "window"}], "count": 3}, {"className": "AutoParseManager", "declarations": [{"file": "ui\\sidepanel\\features\\AutoParseManager.js", "line": 7, "pattern": "class AutoParseManager {", "type": "class"}, {"file": "ui\\sidepanel\\features\\AutoParseManager.js", "line": 620, "pattern": "window.AutoParseManager =", "type": "window"}], "count": 2}, {"className": "StateManager", "declarations": [{"file": "ui\\sidepanel\\core\\StateManager.js", "line": 7, "pattern": "class StateManager {", "type": "class"}, {"file": "ui\\sidepanel\\core\\StateManager.js", "line": 412, "pattern": "window.StateManager =", "type": "window"}], "count": 2}, {"className": "ModuleRegistry", "declarations": [{"file": "ui\\sidepanel\\core\\ModuleRegistry.js", "line": 7, "pattern": "class ModuleRegistry {", "type": "class"}, {"file": "ui\\sidepanel\\core\\ModuleRegistry.js", "line": 415, "pattern": "window.ModuleRegistry =", "type": "window"}], "count": 2}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "declarations": [{"file": "ui\\sidepanel\\core\\ModuleLoader.js", "line": 7, "pattern": "class ModuleLoader {", "type": "class"}, {"file": "ui\\sidepanel\\core\\ModuleLoader.js", "line": 318, "pattern": "window.ModuleLoader =", "type": "window"}], "count": 2}, {"className": "EventManager", "declarations": [{"file": "ui\\sidepanel\\core\\EventManager.js", "line": 7, "pattern": "class EventManager {", "type": "class"}, {"file": "ui\\sidepanel\\core\\EventManager.js", "line": 752, "pattern": "window.EventManager =", "type": "window"}], "count": 2}, {"className": "SidePanelCore", "declarations": [{"file": "ui\\sidepanel\\core\\SidePanelCore.js", "line": 7, "pattern": "class SidePanelCore {", "type": "class"}, {"file": "ui\\sidepanel\\core\\SidePanelCore.js", "line": 583, "pattern": "window.SidePanelCore =", "type": "window"}], "count": 2}, {"className": "ModuleInitializer", "declarations": [{"file": "ui\\sidepanel\\core\\ModuleInitializer.js", "line": 7, "pattern": "class ModuleInitializer {", "type": "class"}, {"file": "ui\\sidepanel\\core\\ModuleInitializer.js", "line": 349, "pattern": "window.ModuleInitializer =", "type": "window"}], "count": 2}, {"className": "ImageProcessor", "declarations": [{"file": "ui\\sidepanel\\ai\\ImageProcessor.js", "line": 7, "pattern": "class ImageProcessor {", "type": "class"}, {"file": "ui\\sidepanel\\ai\\ImageProcessor.js", "line": 638, "pattern": "window.ImageProcessor =", "type": "window"}], "count": 2}, {"className": "ProgressVisualizer", "declarations": [{"file": "ui\\sidepanel\\compatibility\\LegacyAdapter.js", "line": 123, "pattern": "window.ProgressVisualizer =", "type": "window"}, {"file": "ui\\sidepanel\\ui\\ProgressVisualizer.js", "line": 7, "pattern": "class ProgressVisualizer {", "type": "class"}, {"file": "ui\\sidepanel\\ui\\ProgressVisualizer.js", "line": 902, "pattern": "window.ProgressVisualizer =", "type": "window"}], "count": 3}, {"className": "PreviewManager", "declarations": [{"file": "ui\\sidepanel\\data\\PreviewManager.js", "line": 7, "pattern": "class PreviewManager {", "type": "class"}, {"file": "ui\\sidepanel\\data\\PreviewManager.js", "line": 830, "pattern": "window.PreviewManager =", "type": "window"}], "count": 2}], "potentialConflicts": [], "cleanFiles": [], "errors": []}}