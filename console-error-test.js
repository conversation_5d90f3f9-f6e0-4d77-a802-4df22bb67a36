/**
 * Chrome扩展控制台错误测试脚本
 * 用于验证修复后的扩展是否还有控制台错误
 */

const fs = require('fs');
const path = require('path');

class ConsoleErrorTest {
    constructor() {
        this.projectRoot = __dirname;
        this.testResults = {
            moduleConfigTests: [],
            domSelectorTests: [],
            initializationTests: [],
            errors: []
        };
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 [ConsoleErrorTest] 开始控制台错误修复验证测试');
        
        try {
            // 测试1: 模块配置问题修复验证
            await this.testModuleConfigFixes();
            
            // 测试2: DOM选择器修复验证
            await this.testDOMSelectorFixes();
            
            // 测试3: 初始化流程修复验证
            await this.testInitializationFixes();
            
            // 生成测试报告
            await this.generateTestReport();
            
        } catch (error) {
            console.error('❌ [ConsoleErrorTest] 测试过程中发生错误:', error);
            this.testResults.errors.push({
                type: 'test_execution_error',
                message: error.message,
                stack: error.stack
            });
        }
    }

    /**
     * 测试模块配置问题修复
     */
    async testModuleConfigFixes() {
        console.log('🔧 [ConsoleErrorTest] 测试模块配置修复...');
        
        const tests = [
            {
                name: 'SidePanelCore模块查找逻辑',
                file: 'ui/sidepanel/core/SidePanelCore.js',
                checkFunction: this.checkSidePanelCoreModuleLogic.bind(this)
            },
            {
                name: 'ModularSidePanel初始化顺序',
                file: 'ui/sidepanel/ui-sidepanel-modular.js',
                checkFunction: this.checkModularSidePanelInit.bind(this)
            },
            {
                name: 'ModuleRegistry配置完整性',
                file: 'ui/sidepanel/core/ModuleRegistry.js',
                checkFunction: this.checkModuleRegistryConfig.bind(this)
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.checkFunction(test.file);
                this.testResults.moduleConfigTests.push({
                    name: test.name,
                    status: result.success ? 'PASS' : 'FAIL',
                    details: result.details,
                    file: test.file
                });
                console.log(`${result.success ? '✅' : '❌'} ${test.name}: ${result.success ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                this.testResults.moduleConfigTests.push({
                    name: test.name,
                    status: 'ERROR',
                    error: error.message,
                    file: test.file
                });
                console.log(`🔥 ${test.name}: ERROR - ${error.message}`);
            }
        }
    }

    /**
     * 测试DOM选择器修复
     */
    async testDOMSelectorFixes() {
        console.log('🎯 [ConsoleErrorTest] 测试DOM选择器修复...');
        
        const expectedSelectors = [
            '#personalInfoInput',
            '#travelInfoInput', 
            '#imageUploadBtn',
            '#imageInput',
            '#parsePersonalBtn',
            '#parseTravelBtn',
            '#updateToMDACBtn',
            '#clearBtn',
            '#clearAllBtn',
            '#autoParsePersonalEnabled',
            '#autoParseTravel Enabled',
            '#debugConsoleBtn'
        ];

        const eventManagerFile = 'ui/sidepanel/core/EventManager.js';
        const htmlFile = 'ui/ui-sidepanel.html';

        try {
            const eventManagerContent = fs.readFileSync(path.join(this.projectRoot, eventManagerFile), 'utf8');
            const htmlContent = fs.readFileSync(path.join(this.projectRoot, htmlFile), 'utf8');

            let passCount = 0;
            let failCount = 0;

            for (const selector of expectedSelectors) {
                const selectorId = selector.replace('#', '');
                const inEventManager = eventManagerContent.includes(selector);
                const inHTML = htmlContent.includes(`id="${selectorId}"`);

                const testResult = {
                    selector: selector,
                    inEventManager: inEventManager,
                    inHTML: inHTML,
                    status: inEventManager && inHTML ? 'PASS' : 'FAIL'
                };

                this.testResults.domSelectorTests.push(testResult);

                if (testResult.status === 'PASS') {
                    passCount++;
                    console.log(`✅ ${selector}: 在EventManager和HTML中都存在`);
                } else {
                    failCount++;
                    console.log(`❌ ${selector}: EventManager(${inEventManager}) HTML(${inHTML})`);
                }
            }

            console.log(`📊 DOM选择器测试结果: ${passCount}个通过, ${failCount}个失败`);

        } catch (error) {
            this.testResults.errors.push({
                type: 'dom_selector_test_error',
                message: error.message
            });
        }
    }

    /**
     * 测试初始化流程修复
     */
    async testInitializationFixes() {
        console.log('🚀 [ConsoleErrorTest] 测试初始化流程修复...');
        
        const checks = [
            {
                name: 'DOM就绪检查机制',
                check: this.checkDOMReadyMechanism.bind(this)
            },
            {
                name: '模块初始化顺序',
                check: this.checkModuleInitOrder.bind(this)
            },
            {
                name: '错误处理机制',
                check: this.checkErrorHandling.bind(this)
            }
        ];

        for (const check of checks) {
            try {
                const result = await check.check();
                this.testResults.initializationTests.push({
                    name: check.name,
                    status: result.success ? 'PASS' : 'FAIL',
                    details: result.details
                });
                console.log(`${result.success ? '✅' : '❌'} ${check.name}: ${result.success ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                this.testResults.initializationTests.push({
                    name: check.name,
                    status: 'ERROR',
                    error: error.message
                });
                console.log(`🔥 ${check.name}: ERROR - ${error.message}`);
            }
        }
    }

    /**
     * 检查SidePanelCore模块逻辑
     */
    async checkSidePanelCoreModuleLogic(filePath) {
        const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8');
        
        const hasModuleRegistrySync = content.includes('moduleRegistry.getAllModules()');
        const hasModuleLoaderRegister = content.includes('moduleLoader.registerModule');
        const hasConfigValidation = content.includes('moduleConfig && window[moduleConfig.globalName]');
        
        return {
            success: hasModuleRegistrySync && hasModuleLoaderRegister && hasConfigValidation,
            details: {
                hasModuleRegistrySync,
                hasModuleLoaderRegister,
                hasConfigValidation
            }
        };
    }

    /**
     * 检查ModularSidePanel初始化
     */
    async checkModularSidePanelInit(filePath) {
        const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8');
        
        const hasCorrectOrder = content.includes('moduleRegistry = new ModuleRegistry()') && 
                               content.includes('moduleRegistry.initializeModuleDefinitions()');
        const hasDOMReady = content.includes('waitForDOMReady()');
        const hasGlobalAssignment = content.includes('window.mdacModuleRegistry');
        
        return {
            success: hasCorrectOrder && hasDOMReady && hasGlobalAssignment,
            details: {
                hasCorrectOrder,
                hasDOMReady,
                hasGlobalAssignment
            }
        };
    }

    /**
     * 检查ModuleRegistry配置
     */
    async checkModuleRegistryConfig(filePath) {
        const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8');
        
        const hasStateManagerConfig = content.includes("registerModule('StateManager'");
        const hasEventManagerConfig = content.includes("registerModule('EventManager'");
        const hasValidationMethod = content.includes('validate()');
        
        return {
            success: hasStateManagerConfig && hasEventManagerConfig && hasValidationMethod,
            details: {
                hasStateManagerConfig,
                hasEventManagerConfig,
                hasValidationMethod
            }
        };
    }

    /**
     * 检查DOM就绪机制
     */
    async checkDOMReadyMechanism() {
        const filePath = 'ui/sidepanel/ui-sidepanel-modular.js';
        const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8');
        
        const hasWaitForDOMReady = content.includes('waitForDOMReady()');
        const hasImplementation = content.includes('document.readyState');
        
        return {
            success: hasWaitForDOMReady && hasImplementation,
            details: {
                hasWaitForDOMReady,
                hasImplementation
            }
        };
    }

    /**
     * 检查模块初始化顺序
     */
    async checkModuleInitOrder() {
        const filePath = 'ui/sidepanel/ui-sidepanel-modular.js';
        const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8');
        
        const registryBeforeLoader = content.indexOf('moduleRegistry = new ModuleRegistry()') < 
                                   content.indexOf('moduleLoader = new ModuleLoader()');
        const stateManagerAfterRegistry = content.indexOf('stateManager = new StateManager') > 
                                        content.indexOf('moduleRegistry = new ModuleRegistry()');
        
        return {
            success: registryBeforeLoader && stateManagerAfterRegistry,
            details: {
                registryBeforeLoader,
                stateManagerAfterRegistry
            }
        };
    }

    /**
     * 检查错误处理机制
     */
    async checkErrorHandling() {
        const filePath = 'ui/sidepanel/core/EventManager.js';
        const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8');
        
        const hasErrorLogging = content.includes('console.log') && content.includes('[EventManager]');
        const hasGracefulDegradation = content.includes('console.warn');
        
        return {
            success: hasErrorLogging && hasGracefulDegradation,
            details: {
                hasErrorLogging,
                hasGracefulDegradation
            }
        };
    }

    /**
     * 生成测试报告
     */
    async generateTestReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                moduleConfigTests: this.testResults.moduleConfigTests.length,
                moduleConfigPassed: this.testResults.moduleConfigTests.filter(t => t.status === 'PASS').length,
                domSelectorTests: this.testResults.domSelectorTests.length,
                domSelectorPassed: this.testResults.domSelectorTests.filter(t => t.status === 'PASS').length,
                initializationTests: this.testResults.initializationTests.length,
                initializationPassed: this.testResults.initializationTests.filter(t => t.status === 'PASS').length,
                totalErrors: this.testResults.errors.length
            },
            details: this.testResults
        };

        const reportPath = path.join(this.projectRoot, 'console-error-test-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');

        console.log('\n📋 [ConsoleErrorTest] 测试结果摘要:');
        console.log(`🔧 模块配置测试: ${report.summary.moduleConfigPassed}/${report.summary.moduleConfigTests} 通过`);
        console.log(`🎯 DOM选择器测试: ${report.summary.domSelectorPassed}/${report.summary.domSelectorTests} 通过`);
        console.log(`🚀 初始化流程测试: ${report.summary.initializationPassed}/${report.summary.initializationTests} 通过`);
        console.log(`🔥 错误数量: ${report.summary.totalErrors}`);
        console.log(`📄 详细报告已保存到: ${reportPath}`);

        return report;
    }
}

// 执行测试
if (require.main === module) {
    const tester = new ConsoleErrorTest();
    tester.runAllTests().then(() => {
        console.log('🎉 [ConsoleErrorTest] 所有测试完成');
    }).catch(error => {
        console.error('💥 [ConsoleErrorTest] 测试失败:', error);
        process.exit(1);
    });
}

module.exports = ConsoleErrorTest;
