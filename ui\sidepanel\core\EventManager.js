/**
 * 事件管理器 - 管理侧边栏的所有事件监听和处理
 * 负责DOM事件、Chrome扩展事件和自定义事件的统一管理
 * 创建日期: 2025-01-11
 */

class EventManager {
    constructor(sidePanelInstance, eventBus = window.mdacEventBus, stateManager = null) {
        this.sidePanel = sidePanelInstance;
        this.eventBus = eventBus;
        this.stateManager = stateManager;
        
        // 事件监听器注册表
        this.eventListeners = new Map();
        
        // DOM事件监听器
        this.domListeners = new Map();
        
        // Chrome扩展事件监听器
        this.chromeListeners = new Map();
        
        // 事件处理状态
        this.isInitialized = false;
        this.isDestroyed = false;

        console.log('🎯 [EventManager] 事件管理器已初始化');
    }

    /**
     * 初始化所有事件监听器
     */
    async initialize() {
        if (this.isInitialized) {
            console.warn('⚠️ [EventManager] 事件管理器已经初始化');
            return;
        }

        try {
            console.log('🎯 [EventManager] 开始初始化事件监听器...');

            // 初始化DOM事件
            this.initializeDOMEvents();
            
            // 初始化Chrome扩展事件
            this.initializeChromeEvents();
            
            // 初始化自定义事件
            this.initializeCustomEvents();
            
            // 初始化状态事件
            this.initializeStateEvents();

            this.isInitialized = true;
            console.log('✅ [EventManager] 事件管理器初始化完成');

            // 发布初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('eventManager:initialized');
            }

        } catch (error) {
            console.error('❌ [EventManager] 事件管理器初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化DOM事件监听器
     */
    initializeDOMEvents() {
        console.log('🖱️ [EventManager] 初始化DOM事件监听器');

        // 延迟DOM绑定，等待DOM完全加载
        const bindDOMEvents = () => {
            // 输入框事件
            this.addDOMListener('#input-text', 'input', (e) => {
                this.handleTextInput(e);
            });

            this.addDOMListener('#input-text', 'paste', (e) => {
                this.handleTextPaste(e);
            });

            // 图片上传事件
            this.addDOMListener('#image-upload', 'change', (e) => {
                this.handleImageUpload(e);
            });

            // 按钮点击事件
            this.addDOMListener('#parse-button', 'click', (e) => {
                this.handleParseButtonClick(e);
            });

            this.addDOMListener('#fill-form-button', 'click', (e) => {
                this.handleFillFormClick(e);
            });

            this.addDOMListener('#clear-button', 'click', (e) => {
                this.handleClearButtonClick(e);
            });

            // 标签页切换事件
            this.addDOMListener('.tab-button', 'click', (e) => {
                this.handleTabSwitch(e);
            }, true); // 使用事件委托

            // 设置相关事件
            this.addDOMListener('#auto-parse-toggle', 'change', (e) => {
                this.handleAutoParseToggle(e);
            });

            this.addDOMListener('#debug-toggle', 'change', (e) => {
                this.handleDebugToggle(e);
            });

            // 键盘快捷键
            this.addDOMListener(document, 'keydown', (e) => {
                this.handleKeyboardShortcuts(e);
            });

            // 拖拽事件
            this.addDOMListener('#input-text', 'dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'copy';
            });

            this.addDOMListener('#input-text', 'drop', (e) => {
                this.handleFileDrop(e);
            });
        };

        // 如果DOM已经加载完成，立即绑定
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            setTimeout(bindDOMEvents, 100); // 小延迟确保DOM完全渲染
        } else {
            // 否则等待DOM加载完成
            document.addEventListener('DOMContentLoaded', bindDOMEvents);
        }
    }

    /**
     * 初始化Chrome扩展事件
     */
    initializeChromeEvents() {
        console.log('🔌 [EventManager] 初始化Chrome扩展事件监听器');

        // 来自content script的消息
        this.addChromeListener(chrome.runtime.onMessage, (message, sender, sendResponse) => {
            this.handleChromeMessage(message, sender, sendResponse);
        });

        // 存储变化事件
        this.addChromeListener(chrome.storage.onChanged, (changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });

        // 标签页更新事件
        if (chrome.tabs && chrome.tabs.onUpdated) {
            this.addChromeListener(chrome.tabs.onUpdated, (tabId, changeInfo, tab) => {
                this.handleTabUpdate(tabId, changeInfo, tab);
            });
        }
    }

    /**
     * 初始化自定义事件
     */
    initializeCustomEvents() {
        console.log('🎨 [EventManager] 初始化自定义事件监听器');

        if (!this.eventBus) return;

        // AI处理事件
        this.eventBus.on('ai:processing-start', (data) => {
            this.handleAIProcessingStart(data);
        });

        this.eventBus.on('ai:processing-complete', (data) => {
            this.handleAIProcessingComplete(data);
        });

        this.eventBus.on('ai:processing-error', (data) => {
            this.handleAIProcessingError(data);
        });

        // 表单填充事件
        this.eventBus.on('form:fill-start', (data) => {
            this.handleFormFillStart(data);
        });

        this.eventBus.on('form:fill-progress', (data) => {
            this.handleFormFillProgress(data);
        });

        this.eventBus.on('form:fill-complete', (data) => {
            this.handleFormFillComplete(data);
        });

        // 数据验证事件
        this.eventBus.on('data:validation-complete', (data) => {
            this.handleDataValidation(data);
        });

        // 错误处理事件
        this.eventBus.on('error:occurred', (data) => {
            this.handleError(data);
        });
    }

    /**
     * 初始化状态事件
     */
    initializeStateEvents() {
        if (!this.stateManager) return;

        console.log('📊 [EventManager] 初始化状态事件监听器');

        // 监听UI状态变化
        this.stateManager.subscribe('ui.currentTab', (newTab, oldTab) => {
            this.handleTabChange(newTab, oldTab);
        });

        this.stateManager.subscribe('ui.isLoading', (isLoading) => {
            this.handleLoadingStateChange(isLoading);
        });

        // 监听数据状态变化
        this.stateManager.subscribe('data.parsedData', (newData, oldData) => {
            this.handleParsedDataChange(newData, oldData);
        });

        // 监听设置变化
        this.stateManager.subscribe('settings', (newSettings, oldSettings) => {
            this.handleSettingsChange(newSettings, oldSettings);
        });
    }

    /**
     * 添加DOM事件监听器
     * @param {string|Element} selector - 选择器或DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {boolean} useCapture - 是否使用捕获
     */
    addDOMListener(selector, event, handler, useCapture = false) {
        const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
        
        if (!element) {
            console.warn(`⚠️ [EventManager] DOM元素未找到: ${selector}`);
            return;
        }

        const wrappedHandler = (e) => {
            try {
                handler(e);
            } catch (error) {
                console.error(`❌ [EventManager] DOM事件处理失败: ${event}`, error);
                this.handleError({ type: 'dom-event-error', error, event, selector });
            }
        };

        element.addEventListener(event, wrappedHandler, useCapture);

        // 记录监听器以便后续清理
        const key = `${selector}-${event}`;
        if (!this.domListeners.has(key)) {
            this.domListeners.set(key, []);
        }
        this.domListeners.get(key).push({ element, event, handler: wrappedHandler, useCapture });
    }

    /**
     * 添加Chrome事件监听器
     * @param {Object} chromeEvent - Chrome事件对象
     * @param {Function} handler - 事件处理函数
     */
    addChromeListener(chromeEvent, handler) {
        if (!chromeEvent || !chromeEvent.addListener) {
            console.warn('⚠️ [EventManager] 无效的Chrome事件对象');
            return;
        }

        const wrappedHandler = (...args) => {
            try {
                return handler(...args);
            } catch (error) {
                console.error('❌ [EventManager] Chrome事件处理失败', error);
                this.handleError({ type: 'chrome-event-error', error, event: chromeEvent });
            }
        };

        chromeEvent.addListener(wrappedHandler);

        // 记录监听器以便后续清理
        this.chromeListeners.set(chromeEvent, wrappedHandler);
    }

    // ==================== 事件处理方法 ====================

    /**
     * 处理文本输入
     */
    handleTextInput(e) {
        const text = e.target.value;
        if (this.stateManager) {
            this.stateManager.set('data.inputText', text);
        }

        // 自动解析检查
        if (this.stateManager && this.stateManager.get('settings.autoParseEnabled')) {
            this.debounceAutoparse(text);
        }
    }

    /**
     * 处理文本粘贴
     */
    handleTextPaste(e) {
        setTimeout(() => {
            const text = e.target.value;
            if (this.stateManager) {
                this.stateManager.set('data.inputText', text);
            }
            
            // 触发自动解析
            if (this.stateManager && this.stateManager.get('settings.autoParseEnabled')) {
                this.triggerAutoparse(text);
            }
        }, 10);
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(e) {
        const file = e.target.files[0];
        if (file && this.sidePanel.handleImageUpload) {
            this.sidePanel.handleImageUpload(file);
        }
    }

    /**
     * 处理解析按钮点击
     */
    handleParseButtonClick(e) {
        e.preventDefault();
        if (this.sidePanel.parseContent) {
            this.sidePanel.parseContent();
        }
    }

    /**
     * 处理表单填充按钮点击
     */
    handleFillFormClick(e) {
        e.preventDefault();
        if (this.sidePanel.fillForm) {
            this.sidePanel.fillForm();
        }
    }

    /**
     * 处理清除按钮点击
     */
    handleClearButtonClick(e) {
        e.preventDefault();
        if (this.sidePanel.clearAll) {
            this.sidePanel.clearAll();
        }
    }

    /**
     * 处理标签页切换
     */
    handleTabSwitch(e) {
        if (e.target.classList.contains('tab-button')) {
            const tabName = e.target.dataset.tab;
            if (tabName && this.stateManager) {
                this.stateManager.set('ui.currentTab', tabName);
            }
        }
    }

    /**
     * 处理自动解析开关
     */
    handleAutoParseToggle(e) {
        const enabled = e.target.checked;
        if (this.stateManager) {
            this.stateManager.set('settings.autoParseEnabled', enabled);
        }
    }

    /**
     * 处理调试模式开关
     */
    handleDebugToggle(e) {
        const enabled = e.target.checked;
        if (this.stateManager) {
            this.stateManager.set('settings.debugMode', enabled);
        }
    }

    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // Ctrl+Enter 或 Cmd+Enter 触发解析
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.handleParseButtonClick(e);
        }
        
        // Ctrl+Shift+F 或 Cmd+Shift+F 触发表单填充
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'F') {
            e.preventDefault();
            this.handleFillFormClick(e);
        }
        
        // Escape 清除内容
        if (e.key === 'Escape') {
            this.handleClearButtonClick(e);
        }
    }

    /**
     * 处理文件拖拽
     */
    handleFileDrop(e) {
        e.preventDefault();
        const files = e.dataTransfer.files;
        
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('image/')) {
                if (this.sidePanel.handleImageUpload) {
                    this.sidePanel.handleImageUpload(file);
                }
            } else if (file.type === 'text/plain') {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const text = e.target.result;
                    if (this.stateManager) {
                        this.stateManager.set('data.inputText', text);
                    }
                };
                reader.readAsText(file);
            }
        }
    }

    /**
     * 处理Chrome消息
     */
    handleChromeMessage(message, sender, sendResponse) {
        console.log('📨 [EventManager] 收到Chrome消息', message);
        
        if (this.sidePanel.handleMessage) {
            return this.sidePanel.handleMessage(message, sender, sendResponse);
        }
    }

    /**
     * 处理存储变化
     */
    handleStorageChange(changes, namespace) {
        console.log('💾 [EventManager] 存储变化', changes, namespace);
        
        if (this.eventBus) {
            this.eventBus.emit('storage:changed', { changes, namespace });
        }
    }

    /**
     * 处理标签页更新
     */
    handleTabUpdate(tabId, changeInfo, tab) {
        if (changeInfo.status === 'complete' && tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
            console.log('🔄 [EventManager] MDAC页面已加载', tab.url);
            
            if (this.eventBus) {
                this.eventBus.emit('mdac:page-loaded', { tabId, tab });
            }
        }
    }

    // ==================== 状态事件处理 ====================

    /**
     * 处理标签页变化
     */
    handleTabChange(newTab, oldTab) {
        console.log(`🔄 [EventManager] 标签页切换: ${oldTab} -> ${newTab}`);
        
        // 更新UI显示
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === newTab);
        });
        
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.dataset.tab === newTab);
        });
    }

    /**
     * 处理加载状态变化
     */
    handleLoadingStateChange(isLoading) {
        const loadingElements = document.querySelectorAll('.loading-indicator');
        loadingElements.forEach(el => {
            el.style.display = isLoading ? 'block' : 'none';
        });
        
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.disabled = isLoading;
        });
    }

    /**
     * 处理解析数据变化
     */
    handleParsedDataChange(newData, oldData) {
        if (newData && this.sidePanel.displayParsedData) {
            this.sidePanel.displayParsedData(newData);
        }
    }

    /**
     * 处理设置变化
     */
    handleSettingsChange(newSettings, oldSettings) {
        console.log('⚙️ [EventManager] 设置已变化', { newSettings, oldSettings });
        
        // 更新UI控件状态
        const autoParseToggle = document.querySelector('#auto-parse-toggle');
        if (autoParseToggle) {
            autoParseToggle.checked = newSettings.autoParseEnabled;
        }
        
        const debugToggle = document.querySelector('#debug-toggle');
        if (debugToggle) {
            debugToggle.checked = newSettings.debugMode;
        }
    }

    // ==================== 自定义事件处理 ====================

    handleAIProcessingStart(data) {
        if (this.stateManager) {
            this.stateManager.set('ai.isProcessing', true);
            this.stateManager.set('ui.isLoading', true);
            this.stateManager.set('ui.loadingMessage', 'AI正在处理...');
        }
    }

    handleAIProcessingComplete(data) {
        if (this.stateManager) {
            this.stateManager.set('ai.isProcessing', false);
            this.stateManager.set('ui.isLoading', false);
            this.stateManager.set('ai.lastResponse', data.response);
        }
    }

    handleAIProcessingError(data) {
        if (this.stateManager) {
            this.stateManager.set('ai.isProcessing', false);
            this.stateManager.set('ui.isLoading', false);
            this.stateManager.set('errors.lastError', data.error);
        }
    }

    handleFormFillStart(data) {
        if (this.stateManager) {
            this.stateManager.set('form.isAutoFilling', true);
            this.stateManager.set('form.fillProgress', 0);
        }
    }

    handleFormFillProgress(data) {
        if (this.stateManager) {
            this.stateManager.set('form.fillProgress', data.progress);
        }
    }

    handleFormFillComplete(data) {
        if (this.stateManager) {
            this.stateManager.set('form.isAutoFilling', false);
            this.stateManager.set('form.fillProgress', 100);
            this.stateManager.set('form.lastFillResult', data.result);
        }
    }

    handleDataValidation(data) {
        if (this.stateManager) {
            this.stateManager.set('data.validationErrors', data.errors || []);
            this.stateManager.set('data.confidence', data.confidence || {});
        }
    }

    handleError(data) {
        console.error('❌ [EventManager] 处理错误', data);
        
        if (this.stateManager) {
            this.stateManager.set('errors.lastError', data);
            
            const errorHistory = this.stateManager.get('errors.errorHistory') || [];
            errorHistory.push({
                ...data,
                timestamp: Date.now()
            });
            
            // 限制错误历史长度
            if (errorHistory.length > 50) {
                errorHistory.shift();
            }
            
            this.stateManager.set('errors.errorHistory', errorHistory);
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 防抖自动解析
     */
    debounceAutoparse(text) {
        if (this.autoparseTimeout) {
            clearTimeout(this.autoparseTimeout);
        }
        
        this.autoparseTimeout = setTimeout(() => {
            this.triggerAutoparse(text);
        }, 1000);
    }

    /**
     * 触发自动解析
     */
    triggerAutoparse(text) {
        if (text && text.trim().length > 10 && this.sidePanel.parseContent) {
            console.log('🤖 [EventManager] 触发自动解析');
            this.sidePanel.parseContent();
        }
    }

    /**
     * 清理所有事件监听器
     */
    destroy() {
        if (this.isDestroyed) return;

        console.log('🗑️ [EventManager] 清理事件监听器');

        // 清理DOM事件监听器
        for (const [key, listeners] of this.domListeners) {
            listeners.forEach(({ element, event, handler, useCapture }) => {
                element.removeEventListener(event, handler, useCapture);
            });
        }
        this.domListeners.clear();

        // 清理Chrome事件监听器
        for (const [chromeEvent, handler] of this.chromeListeners) {
            if (chromeEvent && chromeEvent.removeListener) {
                chromeEvent.removeListener(handler);
            }
        }
        this.chromeListeners.clear();

        // 清理自定义事件监听器
        if (this.eventBus) {
            // 这里应该移除所有通过eventBus.on添加的监听器
            // 但由于EventBus没有提供批量移除的方法，我们依赖EventBus的清理
        }

        // 清理定时器
        if (this.autoparseTimeout) {
            clearTimeout(this.autoparseTimeout);
        }

        this.isDestroyed = true;
        console.log('✅ [EventManager] 事件管理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EventManager;
} else {
    window.EventManager = EventManager;
}

console.log('✅ [EventManager] 事件管理器模块已加载');
