/**
 * 数据管理器 - 统一的数据管理和状态同步
 * 负责管理应用数据、状态同步和数据流控制
 * 创建日期: 2025-01-11
 */

class DataManager {
    constructor(eventBus = window.mdacEventBus, storageService = null, stateManager = null) {
        this.eventBus = eventBus;
        this.storageService = storageService;
        this.stateManager = stateManager;
        
        // 数据存储
        this.data = {
            parsed: null,           // 解析后的数据
            raw: null,             // 原始数据
            validated: null,       // 验证后的数据
            formatted: null,       // 格式化后的数据
            history: [],           // 数据历史
            metadata: {}           // 元数据
        };

        // 数据配置
        this.config = {
            enableHistory: true,
            maxHistorySize: 50,
            enableAutoSave: true,
            autoSaveDelay: 2000,
            enableValidation: true,
            enableFormatting: true
        };

        // 数据状态
        this.dataState = {
            isLoading: false,
            hasData: false,
            isValid: false,
            lastUpdated: null,
            lastSaved: null,
            isDirty: false
        };

        // 数据监听器
        this.dataListeners = new Map();
        this.changeListeners = new Set();
        
        // 自动保存定时器
        this.autoSaveTimer = null;
        
        // 数据统计
        this.stats = {
            totalUpdates: 0,
            totalSaves: 0,
            totalLoads: 0,
            validationErrors: 0,
            lastActivity: null
        };

        console.log('📊 [DataManager] 数据管理器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听数据更新请求
        this.eventBus.on('data:update', (data) => {
            this.handleDataUpdate(data);
        });

        this.eventBus.on('data:set', (data) => {
            this.handleDataSet(data);
        });

        this.eventBus.on('data:get', (data) => {
            this.handleDataGet(data);
        });

        // 监听数据操作请求
        this.eventBus.on('data:save', () => {
            this.saveData();
        });

        this.eventBus.on('data:load', () => {
            this.loadData();
        });

        this.eventBus.on('data:clear', () => {
            this.clearData();
        });

        this.eventBus.on('data:reset', () => {
            this.resetData();
        });

        // 监听AI解析结果
        this.eventBus.on('ai:processing-complete', (result) => {
            this.handleAIResult(result);
        });

        // 监听表单填充结果
        this.eventBus.on('form:fill-complete', (result) => {
            this.handleFormFillResult(result);
        });
    }

    /**
     * 初始化数据管理器
     */
    async initialize() {
        try {
            console.log('📊 [DataManager] 开始初始化');

            // 加载配置
            await this.loadConfiguration();
            
            // 加载保存的数据
            await this.loadData();
            
            // 初始化状态同步
            this.initializeStateSync();

            console.log('✅ [DataManager] 初始化完成');

            // 发布初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('data:initialized', {
                    hasData: this.dataState.hasData,
                    timestamp: Date.now()
                });
            }

        } catch (error) {
            console.error('❌ [DataManager] 初始化失败', error);
            throw error;
        }
    }

    /**
     * 设置数据
     * @param {string} path - 数据路径
     * @param {*} value - 数据值
     * @param {Object} options - 选项
     */
    setData(path, value, options = {}) {
        try {
            console.log(`📊 [DataManager] 设置数据: ${path}`, { value, options });

            const {
                validate = this.config.enableValidation,
                format = this.config.enableFormatting,
                saveHistory = this.config.enableHistory,
                triggerSave = this.config.enableAutoSave
            } = options;

            // 保存历史
            if (saveHistory) {
                this.saveToHistory(path, this.getData(path));
            }

            // 设置数据
            this.setNestedValue(this.data, path, value);

            // 验证数据
            if (validate) {
                this.validateData(path, value);
            }

            // 格式化数据
            if (format) {
                const formattedValue = this.formatData(path, value);
                this.setNestedValue(this.data, path, formattedValue);
            }

            // 更新状态
            this.updateDataState({
                hasData: true,
                lastUpdated: Date.now(),
                isDirty: true
            });

            // 更新统计
            this.stats.totalUpdates++;
            this.stats.lastActivity = Date.now();

            // 同步到状态管理器
            if (this.stateManager) {
                this.stateManager.set(`data.${path}`, this.getData(path));
            }

            // 触发变更监听器
            this.notifyChangeListeners(path, value);

            // 自动保存
            if (triggerSave) {
                this.scheduleAutoSave();
            }

            // 发布数据变更事件
            if (this.eventBus) {
                this.eventBus.emit('data:changed', {
                    path: path,
                    value: value,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [DataManager] 数据设置失败', error);
            throw error;
        }
    }

    /**
     * 获取数据
     * @param {string} path - 数据路径
     * @param {*} defaultValue - 默认值
     */
    getData(path, defaultValue = null) {
        try {
            if (!path) {
                return this.data;
            }

            const value = this.getNestedValue(this.data, path);
            return value !== undefined ? value : defaultValue;

        } catch (error) {
            console.error('❌ [DataManager] 数据获取失败', error);
            return defaultValue;
        }
    }

    /**
     * 更新数据
     * @param {Object} updates - 更新对象
     * @param {Object} options - 选项
     */
    updateData(updates, options = {}) {
        try {
            console.log('📊 [DataManager] 批量更新数据', updates);

            const {
                merge = true,
                validate = this.config.enableValidation
            } = options;

            Object.entries(updates).forEach(([path, value]) => {
                if (merge && typeof value === 'object' && value !== null) {
                    const currentValue = this.getData(path, {});
                    const mergedValue = { ...currentValue, ...value };
                    this.setData(path, mergedValue, { ...options, triggerSave: false });
                } else {
                    this.setData(path, value, { ...options, triggerSave: false });
                }
            });

            // 统一触发保存
            if (this.config.enableAutoSave) {
                this.scheduleAutoSave();
            }

            return true;

        } catch (error) {
            console.error('❌ [DataManager] 数据更新失败', error);
            throw error;
        }
    }

    /**
     * 保存数据
     */
    async saveData() {
        try {
            if (!this.storageService) {
                console.warn('⚠️ [DataManager] 存储服务不可用，跳过保存');
                return false;
            }

            console.log('📊 [DataManager] 保存数据到存储');

            // 准备保存的数据
            const saveData = {
                data: this.data,
                state: this.dataState,
                metadata: {
                    savedAt: Date.now(),
                    version: '1.0'
                }
            };

            // 保存到存储
            await this.storageService.set('appData', saveData, {
                area: 'local',
                prefix: 'data'
            });

            // 更新状态
            this.updateDataState({
                lastSaved: Date.now(),
                isDirty: false
            });

            // 更新统计
            this.stats.totalSaves++;

            // 发布保存完成事件
            if (this.eventBus) {
                this.eventBus.emit('data:saved', {
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [DataManager] 数据保存失败', error);
            
            // 发布保存错误事件
            if (this.eventBus) {
                this.eventBus.emit('data:save-error', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
            
            throw error;
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            if (!this.storageService) {
                console.warn('⚠️ [DataManager] 存储服务不可用，跳过加载');
                return false;
            }

            console.log('📊 [DataManager] 从存储加载数据');

            this.updateDataState({ isLoading: true });

            // 从存储加载
            const savedData = await this.storageService.get('appData', {
                area: 'local',
                prefix: 'data'
            });

            if (savedData && savedData.data) {
                // 恢复数据
                this.data = savedData.data;
                
                // 恢复状态（部分）
                if (savedData.state) {
                    this.updateDataState({
                        hasData: savedData.state.hasData,
                        isValid: savedData.state.isValid,
                        lastSaved: savedData.state.lastSaved
                    });
                }

                console.log('✅ [DataManager] 数据加载完成');
            } else {
                console.log('📊 [DataManager] 没有找到保存的数据');
            }

            // 更新状态
            this.updateDataState({
                isLoading: false,
                lastUpdated: Date.now()
            });

            // 更新统计
            this.stats.totalLoads++;

            // 同步到状态管理器
            if (this.stateManager) {
                this.stateManager.batchUpdate({
                    'data.parsed': this.data.parsed,
                    'data.validated': this.data.validated,
                    'data.hasData': this.dataState.hasData
                });
            }

            // 发布加载完成事件
            if (this.eventBus) {
                this.eventBus.emit('data:loaded', {
                    hasData: this.dataState.hasData,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [DataManager] 数据加载失败', error);
            
            this.updateDataState({ isLoading: false });
            
            // 发布加载错误事件
            if (this.eventBus) {
                this.eventBus.emit('data:load-error', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
            
            return false;
        }
    }

    /**
     * 清除数据
     */
    clearData() {
        console.log('📊 [DataManager] 清除数据');

        // 保存到历史
        if (this.config.enableHistory) {
            this.saveToHistory('full', this.data);
        }

        // 重置数据
        this.data = {
            parsed: null,
            raw: null,
            validated: null,
            formatted: null,
            history: this.data.history, // 保留历史
            metadata: {}
        };

        // 更新状态
        this.updateDataState({
            hasData: false,
            isValid: false,
            isDirty: true,
            lastUpdated: Date.now()
        });

        // 同步到状态管理器
        if (this.stateManager) {
            this.stateManager.batchUpdate({
                'data.parsed': null,
                'data.validated': null,
                'data.hasData': false
            });
        }

        // 触发变更监听器
        this.notifyChangeListeners('*', null);

        // 发布清除事件
        if (this.eventBus) {
            this.eventBus.emit('data:cleared', {
                timestamp: Date.now()
            });
        }
    }

    /**
     * 重置数据
     */
    resetData() {
        console.log('📊 [DataManager] 重置数据');

        // 完全重置
        this.data = {
            parsed: null,
            raw: null,
            validated: null,
            formatted: null,
            history: [],
            metadata: {}
        };

        this.dataState = {
            isLoading: false,
            hasData: false,
            isValid: false,
            lastUpdated: null,
            lastSaved: null,
            isDirty: false
        };

        // 清除定时器
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }

        // 同步到状态管理器
        if (this.stateManager) {
            this.stateManager.batchUpdate({
                'data.parsed': null,
                'data.validated': null,
                'data.hasData': false
            });
        }

        // 发布重置事件
        if (this.eventBus) {
            this.eventBus.emit('data:reset', {
                timestamp: Date.now()
            });
        }
    }

    /**
     * 处理AI结果
     * @param {Object} result - AI处理结果
     */
    handleAIResult(result) {
        if (result.success && result.data) {
            console.log('📊 [DataManager] 处理AI解析结果');

            // 设置原始数据
            this.setData('raw', {
                text: result.originalText,
                image: result.hasImage,
                timestamp: Date.now()
            });

            // 设置解析数据
            this.setData('parsed', result.data);

            // 设置元数据
            this.setData('metadata.ai', {
                confidence: result.confidence,
                processingTime: result.processingTime,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 处理表单填充结果
     * @param {Object} result - 表单填充结果
     */
    handleFormFillResult(result) {
        if (result.success) {
            console.log('📊 [DataManager] 处理表单填充结果');

            // 设置填充元数据
            this.setData('metadata.form', {
                filledFields: result.fillResult.filledFields,
                totalFields: result.fillResult.totalFields,
                duration: result.duration,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 设置嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @param {*} value - 值
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }

        current[keys[keys.length - 1]] = value;
    }

    /**
     * 获取嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     */
    getNestedValue(obj, path) {
        const keys = path.split('.');
        let current = obj;

        for (const key of keys) {
            if (current === null || current === undefined || !(key in current)) {
                return undefined;
            }
            current = current[key];
        }

        return current;
    }

    /**
     * 更新数据状态
     * @param {Object} updates - 状态更新
     */
    updateDataState(updates) {
        this.dataState = { ...this.dataState, ...updates };

        // 同步到状态管理器
        if (this.stateManager) {
            Object.entries(updates).forEach(([key, value]) => {
                this.stateManager.set(`dataState.${key}`, value);
            });
        }
    }

    /**
     * 验证数据
     * @param {string} path - 数据路径
     * @param {*} value - 数据值
     */
    validateData(path, value) {
        // 基础验证逻辑
        try {
            if (path.includes('email') && value) {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(value)) {
                    this.stats.validationErrors++;
                    console.warn(`⚠️ [DataManager] 验证失败: ${path} - 邮箱格式不正确`);
                }
            }

            if (path.includes('phone') && value) {
                const phonePattern = /^\+?[\d\s\-\(\)]{8,15}$/;
                if (!phonePattern.test(value)) {
                    this.stats.validationErrors++;
                    console.warn(`⚠️ [DataManager] 验证失败: ${path} - 电话格式不正确`);
                }
            }

        } catch (error) {
            console.error('❌ [DataManager] 数据验证失败', error);
            this.stats.validationErrors++;
        }
    }

    /**
     * 格式化数据
     * @param {string} path - 数据路径
     * @param {*} value - 数据值
     */
    formatData(path, value) {
        if (!value) return value;

        try {
            // 电话号码格式化
            if (path.includes('phone')) {
                return value.replace(/[\(\)\s\-]/g, '');
            }

            // 邮箱格式化
            if (path.includes('email')) {
                return value.toLowerCase().trim();
            }

            // 姓名格式化
            if (path.includes('name')) {
                return value.trim();
            }

            return value;

        } catch (error) {
            console.error('❌ [DataManager] 数据格式化失败', error);
            return value;
        }
    }

    /**
     * 保存到历史
     * @param {string} path - 数据路径
     * @param {*} value - 数据值
     */
    saveToHistory(path, value) {
        if (!this.config.enableHistory) return;

        const historyEntry = {
            path: path,
            value: value,
            timestamp: Date.now()
        };

        this.data.history.push(historyEntry);

        // 限制历史大小
        if (this.data.history.length > this.config.maxHistorySize) {
            this.data.history.shift();
        }
    }

    /**
     * 计划自动保存
     */
    scheduleAutoSave() {
        if (!this.config.enableAutoSave) return;

        // 清除之前的定时器
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }

        // 设置新的定时器
        this.autoSaveTimer = setTimeout(() => {
            this.saveData().catch(error => {
                console.error('❌ [DataManager] 自动保存失败', error);
            });
        }, this.config.autoSaveDelay);
    }

    /**
     * 通知变更监听器
     * @param {string} path - 数据路径
     * @param {*} value - 数据值
     */
    notifyChangeListeners(path, value) {
        this.changeListeners.forEach(listener => {
            try {
                listener(path, value);
            } catch (error) {
                console.error('❌ [DataManager] 变更监听器执行失败', error);
            }
        });
    }

    /**
     * 添加变更监听器
     * @param {Function} listener - 监听器函数
     */
    addChangeListener(listener) {
        this.changeListeners.add(listener);
        
        return () => {
            this.changeListeners.delete(listener);
        };
    }

    /**
     * 初始化状态同步
     */
    initializeStateSync() {
        if (!this.stateManager) return;

        // 监听状态管理器的变化
        this.stateManager.addListener('data', (newValue, oldValue) => {
            // 同步状态变化到数据管理器
            if (newValue !== oldValue) {
                console.log('📊 [DataManager] 同步状态变化');
            }
        });
    }

    /**
     * 加载配置
     */
    async loadConfiguration() {
        if (!this.storageService) return;

        try {
            const savedConfig = await this.storageService.get('dataManagerConfig', {
                area: 'sync',
                prefix: 'config'
            });

            if (savedConfig) {
                this.config = { ...this.config, ...savedConfig };
                console.log('📊 [DataManager] 配置已加载', this.config);
            }

        } catch (error) {
            console.warn('⚠️ [DataManager] 配置加载失败', error);
        }
    }

    /**
     * 处理数据更新事件
     * @param {Object} data - 事件数据
     */
    handleDataUpdate(data) {
        const { path, value, options } = data;
        this.setData(path, value, options);
    }

    /**
     * 处理数据设置事件
     * @param {Object} data - 事件数据
     */
    handleDataSet(data) {
        const { updates, options } = data;
        this.updateData(updates, options);
    }

    /**
     * 处理数据获取事件
     * @param {Object} data - 事件数据
     */
    handleDataGet(data) {
        const { path, callback } = data;
        const result = this.getData(path);
        if (callback) callback(result);
    }

    /**
     * 获取数据统计
     */
    getStats() {
        return {
            ...this.stats,
            dataSize: JSON.stringify(this.data).length,
            historySize: this.data.history.length,
            hasData: this.dataState.hasData,
            isDirty: this.dataState.isDirty
        };
    }

    /**
     * 获取数据状态
     */
    getDataState() {
        return { ...this.dataState };
    }

    /**
     * 销毁数据管理器
     */
    destroy() {
        // 保存数据
        if (this.dataState.isDirty) {
            this.saveData().catch(error => {
                console.error('❌ [DataManager] 销毁时保存失败', error);
            });
        }

        // 清除定时器
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }

        // 清理监听器
        this.changeListeners.clear();
        this.dataListeners.clear();

        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('data:update');
            this.eventBus.off('data:set');
            this.eventBus.off('data:get');
            this.eventBus.off('data:save');
            this.eventBus.off('data:load');
            this.eventBus.off('data:clear');
            this.eventBus.off('data:reset');
            this.eventBus.off('ai:processing-complete');
            this.eventBus.off('form:fill-complete');
        }

        console.log('🗑️ [DataManager] 数据管理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataManager;
} else {
    window.DataManager = DataManager;
}

console.log('✅ [DataManager] 数据管理器模块已加载');
