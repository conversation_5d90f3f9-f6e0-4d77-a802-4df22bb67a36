/**
 * MDAC字段状态显示模块
 * 用于显示字段填充状态、保留数据和用户指导
 * 创建日期: 2025-01-11
 */

class FieldStatusDisplay {
    constructor() {
        this.statusContainer = null;
        this.preservedDataContainer = null;
        this.isInitialized = false;
        
        console.log('📊 FieldStatusDisplay初始化...');
    }

    /**
     * 初始化状态显示界面
     */
    init() {
        if (this.isInitialized) return;
        
        try {
            this.createStatusContainer();
            this.createPreservedDataContainer();
            this.isInitialized = true;
            console.log('✅ 字段状态显示界面初始化完成');
        } catch (error) {
            console.error('❌ 字段状态显示界面初始化失败:', error);
        }
    }

    /**
     * 创建状态显示容器
     */
    createStatusContainer() {
        // 检查是否已存在
        if (document.getElementById('mdac-field-status')) return;

        const container = document.createElement('div');
        container.id = 'mdac-field-status';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            max-height: 400px;
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            overflow-y: auto;
            display: none;
        `;

        container.innerHTML = `
            <div style="background: #4CAF50; color: white; padding: 12px; border-radius: 6px 6px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: bold;">📊 MDAC填充状态</span>
                <button id="mdac-status-close" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 24px; height: 24px;">×</button>
            </div>
            <div id="mdac-status-content" style="padding: 12px;">
                <div id="mdac-status-summary" style="margin-bottom: 12px; padding: 8px; background: #f5f5f5; border-radius: 4px;"></div>
                <div id="mdac-status-details"></div>
            </div>
        `;

        document.body.appendChild(container);
        this.statusContainer = container;

        // 添加关闭按钮事件
        document.getElementById('mdac-status-close').addEventListener('click', () => {
            this.hideStatus();
        });
    }

    /**
     * 创建保留数据容器
     */
    createPreservedDataContainer() {
        // 检查是否已存在
        if (document.getElementById('mdac-preserved-data')) return;

        const container = document.createElement('div');
        container.id = 'mdac-preserved-data';
        container.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            max-height: 300px;
            background: white;
            border: 2px solid #FF9800;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            overflow-y: auto;
            display: none;
        `;

        container.innerHTML = `
            <div style="background: #FF9800; color: white; padding: 12px; border-radius: 6px 6px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: bold;">📅 保留数据</span>
                <button id="mdac-preserved-close" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 24px; height: 24px;">×</button>
            </div>
            <div id="mdac-preserved-content" style="padding: 12px;">
                <div style="margin-bottom: 8px; color: #666; font-size: 12px;">
                    以下信息无法在当前页面填充，请在MDAC后续步骤中手动输入：
                </div>
                <div id="mdac-preserved-list"></div>
            </div>
        `;

        document.body.appendChild(container);
        this.preservedDataContainer = container;

        // 添加关闭按钮事件
        document.getElementById('mdac-preserved-close').addEventListener('click', () => {
            this.hidePreservedData();
        });
    }

    /**
     * 显示填充状态
     * @param {object} fillStats - 填充统计数据
     * @param {array} filledFields - 已填充字段
     * @param {array} failedFields - 失败字段
     */
    showStatus(fillStats, filledFields = [], failedFields = []) {
        if (!this.isInitialized) this.init();

        const summaryEl = document.getElementById('mdac-status-summary');
        const detailsEl = document.getElementById('mdac-status-details');

        // 显示统计摘要
        summaryEl.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px;">${fillStats.summary}</div>
            <div style="display: flex; justify-content: space-between; font-size: 12px;">
                <span style="color: #4CAF50;">✅ 成功: ${fillStats.successful}</span>
                <span style="color: #FF5722;">❌ 失败: ${fillStats.failed}</span>
                <span style="color: #FF9800;">⚠️ 不可用: ${fillStats.unavailable}</span>
                <span style="color: #9E9E9E;">⏭️ 跳过: ${fillStats.skipped}</span>
            </div>
        `;

        // 显示详细信息
        let detailsHtml = '';
        
        if (filledFields.length > 0) {
            detailsHtml += '<div style="margin-bottom: 12px;"><strong>✅ 成功填充的字段:</strong><ul style="margin: 4px 0; padding-left: 20px;">';
            filledFields.forEach(field => {
                detailsHtml += `<li style="margin: 2px 0; font-size: 12px;">${field.field}: ${field.value}</li>`;
            });
            detailsHtml += '</ul></div>';
        }

        if (failedFields.length > 0) {
            detailsHtml += '<div style="margin-bottom: 12px;"><strong>❌ 填充失败的字段:</strong><ul style="margin: 4px 0; padding-left: 20px;">';
            failedFields.forEach(field => {
                detailsHtml += `<li style="margin: 2px 0; font-size: 12px; color: #FF5722;">${field.field}: ${field.error}</li>`;
            });
            detailsHtml += '</ul></div>';
        }

        detailsEl.innerHTML = detailsHtml;
        this.statusContainer.style.display = 'block';

        // 5秒后自动隐藏
        setTimeout(() => {
            this.hideStatus();
        }, 10000);
    }

    /**
     * 显示保留数据
     * @param {object} preservedData - 保留的数据
     */
    showPreservedData(preservedData) {
        if (!preservedData || Object.keys(preservedData).length === 0) return;
        
        if (!this.isInitialized) this.init();

        const listEl = document.getElementById('mdac-preserved-list');
        let listHtml = '';

        Object.entries(preservedData).forEach(([fieldName, value]) => {
            const displayName = this.getFieldDisplayName(fieldName);
            listHtml += `
                <div style="margin: 8px 0; padding: 8px; background: #fff3cd; border-radius: 4px; border-left: 4px solid #FF9800;">
                    <div style="font-weight: bold; margin-bottom: 4px;">${displayName}</div>
                    <div style="font-family: monospace; background: white; padding: 4px; border-radius: 2px; cursor: pointer;" 
                         onclick="navigator.clipboard.writeText('${value}'); this.style.background='#d4edda'; setTimeout(() => this.style.background='white', 1000);"
                         title="点击复制">
                        ${value}
                    </div>
                    <div style="font-size: 11px; color: #666; margin-top: 4px;">点击上方文本复制到剪贴板</div>
                </div>
            `;
        });

        listEl.innerHTML = listHtml;
        this.preservedDataContainer.style.display = 'block';
    }

    /**
     * 获取字段显示名称
     * @param {string} fieldName - 字段名称
     * @returns {string} 显示名称
     */
    getFieldDisplayName(fieldName) {
        const displayNames = {
            'arrivalDate': '到达日期',
            'departureDate': '离开日期',
            'passportExpiry': '护照有效期',
            'countryCode': '国家代码'
        };
        
        return displayNames[fieldName] || fieldName;
    }

    /**
     * 隐藏状态显示
     */
    hideStatus() {
        if (this.statusContainer) {
            this.statusContainer.style.display = 'none';
        }
    }

    /**
     * 隐藏保留数据显示
     */
    hidePreservedData() {
        if (this.preservedDataContainer) {
            this.preservedDataContainer.style.display = 'none';
        }
    }

    /**
     * 清理界面
     */
    cleanup() {
        this.hideStatus();
        this.hidePreservedData();
        
        if (this.statusContainer) {
            this.statusContainer.remove();
            this.statusContainer = null;
        }
        
        if (this.preservedDataContainer) {
            this.preservedDataContainer.remove();
            this.preservedDataContainer = null;
        }
        
        this.isInitialized = false;
    }
}

// 创建全局实例
window.fieldStatusDisplay = new FieldStatusDisplay();
