/**
 * MDAC Chrome扩展 - 模块化侧边栏主文件
 * 集成所有模块，提供统一的初始化和管理
 * 创建日期: 2025-01-11
 * 版本: 2.0.0 (模块化重构版本)
 */

class MDACModularSidePanel {
    constructor() {
        // 版本信息
        this.version = '2.0.0';
        this.buildDate = '2025-01-11';
        
        // 模块实例
        this.modules = {
            // 核心模块
            eventManager: null,
            stateManager: null,
            moduleLoader: null,
            moduleRegistry: null,
            sidePanelCore: null,
            moduleInitializer: null,
            
            // AI功能模块
            aiService: null,
            textParser: null,
            imageProcessor: null,
            
            // 表单处理模块
            formFiller: null,
            fieldMatcher: null,
            dataValidator: null,
            
            // UI组件模块
            uiRenderer: null,
            modalManager: null,
            progressVisualizer: null,
            
            // 特色功能模块
            autoParseManager: null,
            confidenceEvaluator: null,
            cityViewer: null,
            
            // 数据管理模块
            dataManager: null,
            storageService: null,
            previewManager: null,
            
            // 工具模块
            dateFormatter: null,
            messageHelper: null,
            debugLogger: null,

            // 兼容性适配器
            legacyAdapter: null
        };

        // 初始化状态
        this.initializationState = {
            isInitializing: false,
            isInitialized: false,
            initializationProgress: 0,
            failedModules: [],
            initializationTime: null
        };

        // 配置
        this.config = {
            enableDebugMode: false,
            enablePerformanceMonitoring: true,
            enableErrorReporting: true,
            moduleLoadTimeout: 30000,
            retryFailedModules: true,
            maxRetryAttempts: 3
        };

        console.log('🚀 [MDACModularSidePanel] 模块化侧边栏初始化开始', {
            version: this.version,
            buildDate: this.buildDate
        });

        // 开始初始化
        this.initialize();
    }

    /**
     * 初始化模块化侧边栏
     */
    async initialize() {
        if (this.initializationState.isInitializing || this.initializationState.isInitialized) {
            console.warn('⚠️ [MDACModularSidePanel] 已经在初始化或已初始化');
            return;
        }

        try {
            this.initializationState.isInitializing = true;
            this.initializationState.initializationTime = Date.now();

            console.log('🔧 [MDACModularSidePanel] 开始模块化初始化');

            // 阶段1: 初始化核心模块
            await this.initializeCoreModules();
            this.updateProgress(20);

            // 阶段2: 初始化工具模块
            await this.initializeUtilityModules();
            this.updateProgress(40);

            // 阶段3: 初始化数据管理模块
            await this.initializeDataModules();
            this.updateProgress(60);

            // 阶段4: 初始化功能模块
            await this.initializeFunctionalModules();
            this.updateProgress(80);

            // 阶段5: 初始化UI模块
            await this.initializeUIModules();
            this.updateProgress(90);

            // 阶段6: 完成初始化
            await this.finalizeInitialization();
            this.updateProgress(100);

            this.initializationState.isInitialized = true;
            this.initializationState.isInitializing = false;

            const initTime = Date.now() - this.initializationState.initializationTime;
            console.log('✅ [MDACModularSidePanel] 模块化初始化完成', {
                initializationTime: initTime,
                totalModules: Object.keys(this.modules).length,
                failedModules: this.initializationState.failedModules.length
            });

            // 发布初始化完成事件
            if (this.modules.eventManager) {
                this.modules.eventManager.emit('sidepanel:initialized', {
                    version: this.version,
                    initializationTime: initTime,
                    modules: Object.keys(this.modules),
                    failedModules: this.initializationState.failedModules
                });
            }

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 初始化失败', error);
            this.initializationState.isInitializing = false;
            this.handleInitializationError(error);
            throw error;
        }
    }

    /**
     * 初始化核心模块
     */
    async initializeCoreModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化核心模块');

        try {
            // 首先确保EventBus可用
            if (!window.mdacEventBus) {
                window.mdacEventBus = new EventBus();
                console.log('🚌 [MDACModularSidePanel] 创建全局事件总线');
            }

            // 事件管理器 - 最先初始化，传递this作为sidePanelInstance
            this.modules.eventManager = new EventManager(this, window.mdacEventBus);
            await this.modules.eventManager.initialize();

            // 状态管理器
            this.modules.stateManager = new StateManager(window.mdacEventBus);
            // StateManager在构造函数中自动初始化

            // 模块注册器
            this.modules.moduleRegistry = new ModuleRegistry();
            // ModuleRegistry不需要异步初始化

            // 模块加载器
            this.modules.moduleLoader = new ModuleLoader();
            // ModuleLoader不需要异步初始化

            // 侧边栏核心
            this.modules.sidePanelCore = new SidePanelCore();
            await this.modules.sidePanelCore.initialize();

            // 模块初始化器
            this.modules.moduleInitializer = new ModuleInitializer();
            // ModuleInitializer有自己的初始化方法

            console.log('✅ [MDACModularSidePanel] 核心模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 核心模块初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化工具模块
     */
    async initializeUtilityModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化工具模块');

        try {
            // 调试日志器
            this.modules.debugLogger = new DebugLogger(window.mdacEventBus);
            
            // 日期格式化器
            this.modules.dateFormatter = new DateFormatter();
            
            // 消息助手
            this.modules.messageHelper = new MessageHelper(window.mdacEventBus);

            console.log('✅ [MDACModularSidePanel] 工具模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 工具模块初始化失败', error);
            this.initializationState.failedModules.push('utilities');
        }
    }

    /**
     * 初始化数据管理模块
     */
    async initializeDataModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化数据管理模块');

        try {
            // 存储服务
            this.modules.storageService = new StorageService(window.mdacEventBus);
            
            // 数据管理器
            this.modules.dataManager = new DataManager(
                window.mdacEventBus,
                this.modules.storageService,
                this.modules.stateManager
            );
            await this.modules.dataManager.initialize();

            // 预览管理器
            this.modules.previewManager = new PreviewManager(
                window.mdacEventBus,
                null, // modalManager将在UI模块中初始化
                this.modules.dataManager
            );

            console.log('✅ [MDACModularSidePanel] 数据管理模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 数据管理模块初始化失败', error);
            this.initializationState.failedModules.push('data');
        }
    }

    /**
     * 初始化功能模块
     */
    async initializeFunctionalModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化功能模块');

        try {
            // AI功能模块
            this.modules.textParser = new TextParser(window.mdacEventBus);
            this.modules.imageProcessor = new ImageProcessor(window.mdacEventBus);
            this.modules.aiService = new AIService(
                window.mdacEventBus,
                this.modules.textParser,
                this.modules.imageProcessor
            );
            await this.modules.aiService.initialize();

            // 表单处理模块
            this.modules.dataValidator = new DataValidator(window.mdacEventBus);
            this.modules.fieldMatcher = new FieldMatcher(window.mdacEventBus);
            this.modules.formFiller = new FormFiller(
                window.mdacEventBus,
                this.modules.fieldMatcher,
                this.modules.dataValidator,
                this.modules.messageHelper
            );
            await this.modules.formFiller.initialize();

            // 特色功能模块
            this.modules.confidenceEvaluator = new ConfidenceEvaluator(
                window.mdacEventBus,
                this.modules.dataValidator
            );

            this.modules.autoParseManager = new AutoParseManager(
                window.mdacEventBus,
                this.modules.stateManager,
                this.modules.aiService
            );

            this.modules.cityViewer = new CityViewer(
                window.mdacEventBus,
                null // modalManager将在UI模块中设置
            );
            await this.modules.cityViewer.initialize();

            console.log('✅ [MDACModularSidePanel] 功能模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 功能模块初始化失败', error);
            this.initializationState.failedModules.push('functional');
        }
    }

    /**
     * 初始化UI模块
     */
    async initializeUIModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化UI模块');

        try {
            // 进度可视化器
            this.modules.progressVisualizer = new ProgressVisualizer(window.mdacEventBus);

            // 模态框管理器
            this.modules.modalManager = new ModalManager(
                window.mdacEventBus,
                this.modules.messageHelper
            );

            // UI渲染器
            this.modules.uiRenderer = new UIRenderer(
                window.mdacEventBus,
                this.modules.stateManager,
                this.modules.modalManager,
                this.modules.progressVisualizer
            );
            await this.modules.uiRenderer.initialize();

            // 更新其他模块的modalManager引用
            if (this.modules.previewManager) {
                this.modules.previewManager.modalManager = this.modules.modalManager;
            }
            if (this.modules.cityViewer) {
                this.modules.cityViewer.modalManager = this.modules.modalManager;
            }

            console.log('✅ [MDACModularSidePanel] UI模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] UI模块初始化失败', error);
            this.initializationState.failedModules.push('ui');
        }
    }

    /**
     * 完成初始化
     */
    async finalizeInitialization() {
        console.log('🔧 [MDACModularSidePanel] 完成初始化');

        try {
            // 启动自动解析管理器
            if (this.modules.autoParseManager) {
                this.modules.autoParseManager.start();
            }

            // 设置全局错误处理
            this.setupGlobalErrorHandling();

            // 设置性能监控
            if (this.config.enablePerformanceMonitoring) {
                this.setupPerformanceMonitoring();
            }

            // 注册全局快捷键
            this.setupGlobalShortcuts();

            // 设置模块间通信
            this.setupModuleCommunication();

            // 初始化兼容性适配器
            this.initializeLegacyAdapter();

            console.log('✅ [MDACModularSidePanel] 初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 完成初始化失败', error);
            throw error;
        }
    }

    /**
     * 更新初始化进度
     * @param {number} progress - 进度百分比
     */
    updateProgress(progress) {
        this.initializationState.initializationProgress = progress;
        
        if (this.modules.eventManager) {
            this.modules.eventManager.emit('sidepanel:initialization-progress', {
                progress: progress,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            if (this.modules.debugLogger) {
                this.modules.debugLogger.error('Global Error', event.error, {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            if (this.modules.debugLogger) {
                this.modules.debugLogger.error('Unhandled Promise Rejection', event.reason);
            }
        });
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        if (this.modules.debugLogger) {
            // 监控模块加载性能
            this.modules.debugLogger.startPerformanceMark('module-initialization');
            
            // 定期收集性能指标
            setInterval(() => {
                this.collectPerformanceMetrics();
            }, 60000); // 每分钟收集一次
        }
    }

    /**
     * 收集性能指标
     */
    collectPerformanceMetrics() {
        if (!this.modules.debugLogger) return;

        const metrics = {
            memoryUsage: this.modules.debugLogger.getMemoryUsage(),
            moduleCount: Object.keys(this.modules).length,
            activeModules: Object.values(this.modules).filter(Boolean).length,
            timestamp: Date.now()
        };

        this.modules.debugLogger.logPerformance('System Metrics', metrics);
    }

    /**
     * 设置全局快捷键
     */
    setupGlobalShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+D: 切换调试模式
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                this.toggleDebugMode();
                event.preventDefault();
            }

            // Ctrl+Shift+R: 重新加载模块
            if (event.ctrlKey && event.shiftKey && event.key === 'R') {
                this.reloadModules();
                event.preventDefault();
            }
        });
    }

    /**
     * 设置模块间通信
     */
    setupModuleCommunication() {
        if (!this.modules.eventManager) return;

        // 设置模块状态同步
        this.modules.eventManager.on('module:status-changed', (data) => {
            if (this.modules.stateManager) {
                this.modules.stateManager.set(`modules.${data.moduleName}.status`, data.status);
            }
        });

        // 设置错误报告
        this.modules.eventManager.on('module:error', (data) => {
            if (this.modules.debugLogger) {
                this.modules.debugLogger.error(`Module Error: ${data.moduleName}`, data.error);
            }
        });
    }

    /**
     * 初始化兼容性适配器
     */
    initializeLegacyAdapter() {
        try {
            console.log('🔄 [MDACModularSidePanel] 初始化兼容性适配器');

            // 创建兼容性适配器实例
            this.modules.legacyAdapter = new LegacyAdapter(this);

            // 获取兼容性报告
            const compatibilityReport = this.modules.legacyAdapter.getCompatibilityReport();
            console.log('📊 [MDACModularSidePanel] 兼容性报告', compatibilityReport);

            // 如果兼容性低于80%，发出警告
            if (compatibilityReport.compatibilityRate < 0.8) {
                console.warn('⚠️ [MDACModularSidePanel] 兼容性较低，部分旧功能可能不可用');

                if (this.modules.debugLogger) {
                    this.modules.debugLogger.warn('兼容性较低', {
                        compatibilityRate: compatibilityReport.compatibilityRate,
                        incompatible: compatibilityReport.incompatible
                    });
                }
            }

            console.log('✅ [MDACModularSidePanel] 兼容性适配器初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 兼容性适配器初始化失败', error);

            // 即使适配器失败，也不应该阻止主系统运行
            this.modules.legacyAdapter = null;
        }
    }

    /**
     * 切换调试模式
     */
    toggleDebugMode() {
        this.config.enableDebugMode = !this.config.enableDebugMode;
        
        if (this.modules.debugLogger) {
            this.modules.debugLogger.updateConfig({
                level: this.config.enableDebugMode ? 'DEBUG' : 'INFO'
            });
        }

        console.log(`🐛 [MDACModularSidePanel] 调试模式: ${this.config.enableDebugMode ? '启用' : '禁用'}`);
    }

    /**
     * 重新加载模块
     */
    async reloadModules() {
        console.log('🔄 [MDACModularSidePanel] 重新加载模块');
        
        try {
            // 这里可以实现模块的热重载逻辑
            if (this.modules.moduleLoader) {
                await this.modules.moduleLoader.reloadModules();
            }
        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 模块重新加载失败', error);
        }
    }

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError(error) {
        console.error('❌ [MDACModularSidePanel] 初始化错误处理', error);

        // 尝试降级模式
        this.enterDegradedMode();
    }

    /**
     * 进入降级模式
     */
    enterDegradedMode() {
        console.warn('⚠️ [MDACModularSidePanel] 进入降级模式');

        // 只保留核心功能
        const essentialModules = ['eventManager', 'stateManager', 'debugLogger'];
        
        Object.keys(this.modules).forEach(moduleName => {
            if (!essentialModules.includes(moduleName)) {
                this.modules[moduleName] = null;
            }
        });

        // 显示降级模式提示
        if (this.modules.messageHelper) {
            this.modules.messageHelper.error(
                '系统运行在降级模式',
                '部分功能可能不可用，请刷新页面重试'
            );
        }
    }

    /**
     * 获取模块状态
     */
    getModuleStatus() {
        const status = {};
        
        Object.entries(this.modules).forEach(([name, module]) => {
            status[name] = {
                loaded: !!module,
                initialized: module && typeof module.initialize === 'function',
                hasError: this.initializationState.failedModules.includes(name)
            };
        });

        return {
            overall: this.initializationState.isInitialized,
            progress: this.initializationState.initializationProgress,
            modules: status,
            failedModules: this.initializationState.failedModules,
            initializationTime: this.initializationState.initializationTime
        };
    }

    /**
     * 获取版本信息
     */
    getVersionInfo() {
        return {
            version: this.version,
            buildDate: this.buildDate,
            moduleCount: Object.keys(this.modules).length,
            activeModules: Object.values(this.modules).filter(Boolean).length
        };
    }

    /**
     * 销毁模块化侧边栏
     */
    destroy() {
        console.log('🗑️ [MDACModularSidePanel] 销毁模块化侧边栏');

        // 按相反顺序销毁模块
        const destroyOrder = [
            'uiRenderer', 'modalManager', 'progressVisualizer',
            'autoParseManager', 'confidenceEvaluator', 'cityViewer',
            'formFiller', 'fieldMatcher', 'dataValidator',
            'aiService', 'textParser', 'imageProcessor',
            'previewManager', 'dataManager', 'storageService',
            'messageHelper', 'dateFormatter', 'debugLogger',
            'moduleInitializer', 'sidePanelCore', 'moduleLoader',
            'moduleRegistry', 'stateManager', 'eventManager'
        ];

        destroyOrder.forEach(moduleName => {
            const module = this.modules[moduleName];
            if (module && typeof module.destroy === 'function') {
                try {
                    module.destroy();
                } catch (error) {
                    console.error(`❌ [MDACModularSidePanel] 销毁模块失败: ${moduleName}`, error);
                }
            }
            this.modules[moduleName] = null;
        });

        // 重置状态
        this.initializationState = {
            isInitializing: false,
            isInitialized: false,
            initializationProgress: 0,
            failedModules: [],
            initializationTime: null
        };
    }
}

// 全局初始化
let mdacModularSidePanel = null;

// DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        mdacModularSidePanel = new MDACModularSidePanel();
    });
} else {
    mdacModularSidePanel = new MDACModularSidePanel();
}

// 导出到全局
window.mdacModularSidePanel = mdacModularSidePanel;

console.log('✅ [MDACModularSidePanel] 模块化侧边栏主文件已加载');
