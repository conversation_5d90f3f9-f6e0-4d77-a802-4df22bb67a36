/**
 * Chrome扩展重复声明检查器
 * 专门检查是否存在重复的类声明问题
 */

const fs = require('fs');
const path = require('path');

class DuplicateDeclarationChecker {
    constructor() {
        this.projectRoot = __dirname;
        this.checkResults = {
            duplicateClasses: [],
            potentialConflicts: [],
            cleanFiles: [],
            errors: []
        };
        
        // 需要检查的类名列表（基于控制台错误）
        this.classesToCheck = [
            'EventBus',
            'DebugLogger',
            'DateFormatter', 
            'MessageHelper',
            'StorageService',
            'DataValidator',
            'ModalManager',
            'DataManager',
            'FieldMatcher',
            'AIService',
            'UIRenderer',
            'FormFiller',
            'CityViewer',
            'TextParser',
            'ConfidenceEvaluator',
            'AutoParseManager',
            'StateManager',
            'ModuleRegistry',
            'ModuleLoader',
            'EventManager',
            'SidePanelCore',
            'ModuleInitializer',
            'ImageProcessor',
            'ProgressVisualizer',
            'PreviewManager'
        ];
    }

    /**
     * 运行重复声明检查
     */
    async runCheck() {
        console.log('🔍 [DuplicateDeclarationChecker] 开始检查重复类声明');
        
        try {
            // 1. 扫描所有JavaScript文件
            const jsFiles = await this.findJavaScriptFiles();
            console.log(`📁 [DuplicateDeclarationChecker] 找到${jsFiles.length}个JavaScript文件`);
            
            // 2. 检查每个类的声明
            for (const className of this.classesToCheck) {
                await this.checkClassDeclarations(className, jsFiles);
            }
            
            // 3. 生成检查报告
            await this.generateReport();
            
        } catch (error) {
            console.error('❌ [DuplicateDeclarationChecker] 检查过程中发生错误:', error);
            this.checkResults.errors.push({
                type: 'check_execution_error',
                message: error.message,
                stack: error.stack
            });
        }
    }

    /**
     * 查找所有JavaScript文件
     */
    async findJavaScriptFiles() {
        const jsFiles = [];
        
        const searchDirs = [
            'ui/sidepanel',
            'modules',
            'utils',
            'config'
        ];
        
        for (const dir of searchDirs) {
            const fullDir = path.join(this.projectRoot, dir);
            if (fs.existsSync(fullDir)) {
                await this.scanDirectory(fullDir, jsFiles);
            }
        }
        
        // 添加主要的HTML文件（可能包含内联脚本）
        const htmlFiles = ['ui/ui-sidepanel.html'];
        for (const htmlFile of htmlFiles) {
            const fullPath = path.join(this.projectRoot, htmlFile);
            if (fs.existsSync(fullPath)) {
                jsFiles.push(fullPath);
            }
        }
        
        return jsFiles;
    }

    /**
     * 递归扫描目录
     */
    async scanDirectory(dir, jsFiles) {
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    await this.scanDirectory(fullPath, jsFiles);
                } else if (item.endsWith('.js') || item.endsWith('.html')) {
                    jsFiles.push(fullPath);
                }
            }
        } catch (error) {
            console.warn(`⚠️ [DuplicateDeclarationChecker] 无法扫描目录 ${dir}:`, error.message);
        }
    }

    /**
     * 检查特定类的声明
     */
    async checkClassDeclarations(className, jsFiles) {
        console.log(`🔍 [DuplicateDeclarationChecker] 检查类: ${className}`);
        
        const declarations = [];
        
        for (const filePath of jsFiles) {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const relativePath = path.relative(this.projectRoot, filePath);
                
                // 检查类声明模式
                const patterns = [
                    new RegExp(`class\\s+${className}\\s*\\{`, 'g'),
                    new RegExp(`function\\s+${className}\\s*\\(`, 'g'),
                    new RegExp(`const\\s+${className}\\s*=`, 'g'),
                    new RegExp(`let\\s+${className}\\s*=`, 'g'),
                    new RegExp(`var\\s+${className}\\s*=`, 'g'),
                    new RegExp(`window\\.${className}\\s*=`, 'g')
                ];
                
                for (const pattern of patterns) {
                    let match;
                    while ((match = pattern.exec(content)) !== null) {
                        const lineNumber = this.getLineNumber(content, match.index);
                        declarations.push({
                            file: relativePath,
                            line: lineNumber,
                            pattern: match[0],
                            type: this.getDeclarationType(match[0])
                        });
                    }
                }
                
            } catch (error) {
                console.warn(`⚠️ [DuplicateDeclarationChecker] 无法读取文件 ${filePath}:`, error.message);
            }
        }
        
        // 分析声明结果
        if (declarations.length === 0) {
            console.log(`⚠️ [DuplicateDeclarationChecker] 类 ${className} 未找到声明`);
        } else if (declarations.length === 1) {
            console.log(`✅ [DuplicateDeclarationChecker] 类 ${className} 声明正常`);
            this.checkResults.cleanFiles.push({
                className,
                declaration: declarations[0]
            });
        } else {
            console.log(`❌ [DuplicateDeclarationChecker] 类 ${className} 存在重复声明 (${declarations.length}次)`);
            this.checkResults.duplicateClasses.push({
                className,
                declarations,
                count: declarations.length
            });
            
            // 输出详细信息
            declarations.forEach((decl, index) => {
                console.log(`   ${index + 1}. ${decl.file}:${decl.line} - ${decl.type}`);
            });
        }
    }

    /**
     * 获取行号
     */
    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }

    /**
     * 获取声明类型
     */
    getDeclarationType(pattern) {
        if (pattern.includes('class')) return 'class';
        if (pattern.includes('function')) return 'function';
        if (pattern.includes('const')) return 'const';
        if (pattern.includes('let')) return 'let';
        if (pattern.includes('var')) return 'var';
        if (pattern.includes('window.')) return 'window';
        return 'unknown';
    }

    /**
     * 生成检查报告
     */
    async generateReport() {
        console.log('📊 [DuplicateDeclarationChecker] 生成检查报告');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalClasses: this.classesToCheck.length,
                duplicateClasses: this.checkResults.duplicateClasses.length,
                cleanClasses: this.checkResults.cleanFiles.length,
                errors: this.checkResults.errors.length
            },
            details: this.checkResults
        };

        // 保存报告到文件
        const reportPath = path.join(this.projectRoot, 'duplicate-declaration-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        // 输出摘要
        console.log('\n📋 [DuplicateDeclarationChecker] 检查结果摘要:');
        console.log(`📁 检查类总数: ${report.summary.totalClasses}`);
        console.log(`✅ 正常声明: ${report.summary.cleanClasses}`);
        console.log(`❌ 重复声明: ${report.summary.duplicateClasses}`);
        console.log(`🔥 错误数量: ${report.summary.errors}`);
        
        if (this.checkResults.duplicateClasses.length > 0) {
            console.log('\n❌ 存在重复声明的类:');
            this.checkResults.duplicateClasses.forEach(dup => {
                console.log(`   - ${dup.className} (${dup.count}次声明)`);
            });
        } else {
            console.log('\n🎉 未发现重复声明问题！');
        }
        
        console.log(`\n📄 详细报告已保存到: ${reportPath}`);
        
        return report;
    }
}

// 执行检查
if (require.main === module) {
    const checker = new DuplicateDeclarationChecker();
    checker.runCheck().then(() => {
        console.log('🎉 [DuplicateDeclarationChecker] 检查完成');
    }).catch(error => {
        console.error('💥 [DuplicateDeclarationChecker] 检查失败:', error);
        process.exit(1);
    });
}

module.exports = DuplicateDeclarationChecker;
