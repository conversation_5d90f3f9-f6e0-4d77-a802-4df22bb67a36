/**
 * 存储服务 - 统一的数据存储和管理
 * 负责Chrome存储API的封装和数据持久化
 * 创建日期: 2025-01-11
 */

class StorageService {
    constructor(eventBus = window.mdacEventBus) {
        this.eventBus = eventBus;
        
        // 存储配置
        this.config = {
            enableSync: true,
            enableLocal: true,
            enableSession: true,
            enableCache: true,
            cacheTimeout: 300000, // 5分钟
            maxCacheSize: 100
        };

        // 缓存系统
        this.cache = new Map();
        this.cacheTimestamps = new Map();
        
        // 存储统计
        this.stats = {
            syncOperations: 0,
            localOperations: 0,
            sessionOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };

        // 存储键前缀
        this.keyPrefixes = {
            config: 'mdac_config_',
            data: 'mdac_data_',
            cache: 'mdac_cache_',
            temp: 'mdac_temp_',
            user: 'mdac_user_'
        };

        console.log('💾 [StorageService] 存储服务已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听存储请求
        this.eventBus.on('storage:set', (data) => {
            this.handleSetRequest(data);
        });

        this.eventBus.on('storage:get', (data) => {
            this.handleGetRequest(data);
        });

        this.eventBus.on('storage:remove', (data) => {
            this.handleRemoveRequest(data);
        });

        this.eventBus.on('storage:clear', (data) => {
            this.handleClearRequest(data);
        });

        // 监听存储变化
        if (chrome.storage && chrome.storage.onChanged) {
            chrome.storage.onChanged.addListener((changes, areaName) => {
                this.handleStorageChange(changes, areaName);
            });
        }
    }

    /**
     * 设置数据
     * @param {string} key - 键
     * @param {*} value - 值
     * @param {Object} options - 选项
     */
    async set(key, value, options = {}) {
        try {
            const {
                area = 'sync',
                prefix = 'data',
                ttl = null,
                compress = false,
                encrypt = false
            } = options;

            console.log(`💾 [StorageService] 设置数据: ${key}`, { area, prefix });

            // 构建完整键名
            const fullKey = this.buildKey(key, prefix);

            // 处理数据
            let processedValue = value;
            
            if (compress) {
                processedValue = await this.compressData(processedValue);
            }
            
            if (encrypt) {
                processedValue = await this.encryptData(processedValue);
            }

            // 添加元数据
            const dataWithMeta = {
                value: processedValue,
                timestamp: Date.now(),
                ttl: ttl,
                compressed: compress,
                encrypted: encrypt,
                version: '1.0'
            };

            // 存储到指定区域
            await this.setToArea(area, fullKey, dataWithMeta);

            // 更新缓存
            if (this.config.enableCache) {
                this.updateCache(fullKey, dataWithMeta);
            }

            // 更新统计
            this.updateStats(area, 'set');

            // 发布设置完成事件
            if (this.eventBus) {
                this.eventBus.emit('storage:set-complete', {
                    key: key,
                    fullKey: fullKey,
                    area: area,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [StorageService] 数据设置失败', error);
            this.stats.errors++;
            
            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('storage:error', {
                    operation: 'set',
                    key: key,
                    error: error.message,
                    timestamp: Date.now()
                });
            }
            
            throw error;
        }
    }

    /**
     * 获取数据
     * @param {string} key - 键
     * @param {Object} options - 选项
     */
    async get(key, options = {}) {
        try {
            const {
                area = 'sync',
                prefix = 'data',
                defaultValue = null,
                useCache = true
            } = options;

            console.log(`💾 [StorageService] 获取数据: ${key}`, { area, prefix });

            // 构建完整键名
            const fullKey = this.buildKey(key, prefix);

            // 检查缓存
            if (useCache && this.config.enableCache) {
                const cachedData = this.getFromCache(fullKey);
                if (cachedData !== null) {
                    this.stats.cacheHits++;
                    return cachedData.value;
                }
                this.stats.cacheMisses++;
            }

            // 从存储区域获取
            const dataWithMeta = await this.getFromArea(area, fullKey);

            if (!dataWithMeta) {
                return defaultValue;
            }

            // 检查TTL
            if (dataWithMeta.ttl && Date.now() - dataWithMeta.timestamp > dataWithMeta.ttl) {
                console.log(`⏰ [StorageService] 数据已过期: ${key}`);
                await this.remove(key, { area, prefix });
                return defaultValue;
            }

            // 处理数据
            let processedValue = dataWithMeta.value;

            if (dataWithMeta.encrypted) {
                processedValue = await this.decryptData(processedValue);
            }

            if (dataWithMeta.compressed) {
                processedValue = await this.decompressData(processedValue);
            }

            // 更新缓存
            if (useCache && this.config.enableCache) {
                this.updateCache(fullKey, dataWithMeta);
            }

            // 更新统计
            this.updateStats(area, 'get');

            return processedValue;

        } catch (error) {
            console.error('❌ [StorageService] 数据获取失败', error);
            this.stats.errors++;
            
            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('storage:error', {
                    operation: 'get',
                    key: key,
                    error: error.message,
                    timestamp: Date.now()
                });
            }
            
            return options.defaultValue || null;
        }
    }

    /**
     * 移除数据
     * @param {string} key - 键
     * @param {Object} options - 选项
     */
    async remove(key, options = {}) {
        try {
            const {
                area = 'sync',
                prefix = 'data'
            } = options;

            console.log(`💾 [StorageService] 移除数据: ${key}`, { area, prefix });

            // 构建完整键名
            const fullKey = this.buildKey(key, prefix);

            // 从存储区域移除
            await this.removeFromArea(area, fullKey);

            // 从缓存移除
            this.removeFromCache(fullKey);

            // 更新统计
            this.updateStats(area, 'remove');

            // 发布移除完成事件
            if (this.eventBus) {
                this.eventBus.emit('storage:remove-complete', {
                    key: key,
                    fullKey: fullKey,
                    area: area,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [StorageService] 数据移除失败', error);
            this.stats.errors++;
            throw error;
        }
    }

    /**
     * 清除数据
     * @param {Object} options - 选项
     */
    async clear(options = {}) {
        try {
            const {
                area = 'sync',
                prefix = null,
                confirm = false
            } = options;

            if (!confirm) {
                throw new Error('清除操作需要确认');
            }

            console.log(`💾 [StorageService] 清除数据`, { area, prefix });

            if (prefix) {
                // 清除特定前缀的数据
                await this.clearByPrefix(area, prefix);
            } else {
                // 清除整个存储区域
                await this.clearArea(area);
            }

            // 清除相关缓存
            if (prefix) {
                this.clearCacheByPrefix(prefix);
            } else {
                this.clearCache();
            }

            // 发布清除完成事件
            if (this.eventBus) {
                this.eventBus.emit('storage:clear-complete', {
                    area: area,
                    prefix: prefix,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [StorageService] 数据清除失败', error);
            this.stats.errors++;
            throw error;
        }
    }

    /**
     * 批量操作
     * @param {Array} operations - 操作列表
     */
    async batch(operations) {
        try {
            console.log(`💾 [StorageService] 执行批量操作: ${operations.length} 个`);

            const results = [];
            
            for (const operation of operations) {
                const { type, key, value, options } = operation;
                
                try {
                    let result;
                    switch (type) {
                        case 'set':
                            result = await this.set(key, value, options);
                            break;
                        case 'get':
                            result = await this.get(key, options);
                            break;
                        case 'remove':
                            result = await this.remove(key, options);
                            break;
                        default:
                            throw new Error(`未知的操作类型: ${type}`);
                    }
                    
                    results.push({
                        success: true,
                        operation: operation,
                        result: result
                    });
                    
                } catch (error) {
                    results.push({
                        success: false,
                        operation: operation,
                        error: error.message
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            console.log(`✅ [StorageService] 批量操作完成: ${successCount}/${operations.length} 成功`);

            return results;

        } catch (error) {
            console.error('❌ [StorageService] 批量操作失败', error);
            throw error;
        }
    }

    /**
     * 存储到指定区域
     * @param {string} area - 存储区域
     * @param {string} key - 键
     * @param {*} value - 值
     */
    async setToArea(area, key, value) {
        const data = { [key]: value };
        
        switch (area) {
            case 'sync':
                if (this.config.enableSync && chrome.storage.sync) {
                    await chrome.storage.sync.set(data);
                } else {
                    throw new Error('Sync存储不可用');
                }
                break;
                
            case 'local':
                if (this.config.enableLocal && chrome.storage.local) {
                    await chrome.storage.local.set(data);
                } else {
                    throw new Error('Local存储不可用');
                }
                break;
                
            case 'session':
                if (this.config.enableSession && chrome.storage.session) {
                    await chrome.storage.session.set(data);
                } else {
                    // 降级到sessionStorage
                    sessionStorage.setItem(key, JSON.stringify(value));
                }
                break;
                
            default:
                throw new Error(`未知的存储区域: ${area}`);
        }
    }

    /**
     * 从指定区域获取
     * @param {string} area - 存储区域
     * @param {string} key - 键
     */
    async getFromArea(area, key) {
        let result;
        
        switch (area) {
            case 'sync':
                if (this.config.enableSync && chrome.storage.sync) {
                    result = await chrome.storage.sync.get(key);
                } else {
                    return null;
                }
                break;
                
            case 'local':
                if (this.config.enableLocal && chrome.storage.local) {
                    result = await chrome.storage.local.get(key);
                } else {
                    return null;
                }
                break;
                
            case 'session':
                if (this.config.enableSession && chrome.storage.session) {
                    result = await chrome.storage.session.get(key);
                } else {
                    // 降级到sessionStorage
                    const data = sessionStorage.getItem(key);
                    return data ? JSON.parse(data) : null;
                }
                break;
                
            default:
                throw new Error(`未知的存储区域: ${area}`);
        }
        
        return result[key] || null;
    }

    /**
     * 从指定区域移除
     * @param {string} area - 存储区域
     * @param {string} key - 键
     */
    async removeFromArea(area, key) {
        switch (area) {
            case 'sync':
                if (chrome.storage.sync) {
                    await chrome.storage.sync.remove(key);
                }
                break;
                
            case 'local':
                if (chrome.storage.local) {
                    await chrome.storage.local.remove(key);
                }
                break;
                
            case 'session':
                if (chrome.storage.session) {
                    await chrome.storage.session.remove(key);
                } else {
                    sessionStorage.removeItem(key);
                }
                break;
        }
    }

    /**
     * 清除存储区域
     * @param {string} area - 存储区域
     */
    async clearArea(area) {
        switch (area) {
            case 'sync':
                if (chrome.storage.sync) {
                    await chrome.storage.sync.clear();
                }
                break;
                
            case 'local':
                if (chrome.storage.local) {
                    await chrome.storage.local.clear();
                }
                break;
                
            case 'session':
                if (chrome.storage.session) {
                    await chrome.storage.session.clear();
                } else {
                    sessionStorage.clear();
                }
                break;
        }
    }

    /**
     * 按前缀清除
     * @param {string} area - 存储区域
     * @param {string} prefix - 前缀
     */
    async clearByPrefix(area, prefix) {
        // 获取所有键
        let allData;
        
        switch (area) {
            case 'sync':
                allData = await chrome.storage.sync.get(null);
                break;
            case 'local':
                allData = await chrome.storage.local.get(null);
                break;
            case 'session':
                if (chrome.storage.session) {
                    allData = await chrome.storage.session.get(null);
                } else {
                    allData = {};
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        allData[key] = sessionStorage.getItem(key);
                    }
                }
                break;
        }

        // 找到匹配前缀的键
        const keysToRemove = Object.keys(allData).filter(key => 
            key.startsWith(this.keyPrefixes[prefix] || prefix)
        );

        // 批量移除
        for (const key of keysToRemove) {
            await this.removeFromArea(area, key);
        }
    }

    /**
     * 构建键名
     * @param {string} key - 原始键
     * @param {string} prefix - 前缀类型
     */
    buildKey(key, prefix) {
        const prefixStr = this.keyPrefixes[prefix] || prefix + '_';
        return prefixStr + key;
    }

    /**
     * 更新缓存
     * @param {string} key - 键
     * @param {*} data - 数据
     */
    updateCache(key, data) {
        if (this.cache.size >= this.config.maxCacheSize) {
            // 移除最旧的缓存项
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
            this.cacheTimestamps.delete(oldestKey);
        }

        this.cache.set(key, data);
        this.cacheTimestamps.set(key, Date.now());
    }

    /**
     * 从缓存获取
     * @param {string} key - 键
     */
    getFromCache(key) {
        if (!this.cache.has(key)) {
            return null;
        }

        const timestamp = this.cacheTimestamps.get(key);
        if (Date.now() - timestamp > this.config.cacheTimeout) {
            this.cache.delete(key);
            this.cacheTimestamps.delete(key);
            return null;
        }

        return this.cache.get(key);
    }

    /**
     * 从缓存移除
     * @param {string} key - 键
     */
    removeFromCache(key) {
        this.cache.delete(key);
        this.cacheTimestamps.delete(key);
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        this.cacheTimestamps.clear();
    }

    /**
     * 按前缀清除缓存
     * @param {string} prefix - 前缀
     */
    clearCacheByPrefix(prefix) {
        const prefixStr = this.keyPrefixes[prefix] || prefix + '_';
        
        for (const key of this.cache.keys()) {
            if (key.startsWith(prefixStr)) {
                this.cache.delete(key);
                this.cacheTimestamps.delete(key);
            }
        }
    }

    /**
     * 更新统计
     * @param {string} area - 存储区域
     * @param {string} operation - 操作类型
     */
    updateStats(area, operation) {
        const statKey = area + 'Operations';
        if (this.stats[statKey] !== undefined) {
            this.stats[statKey]++;
        }
    }

    /**
     * 压缩数据
     * @param {*} data - 数据
     */
    async compressData(data) {
        // 简单的JSON压缩（实际项目中可以使用更好的压缩算法）
        return JSON.stringify(data);
    }

    /**
     * 解压数据
     * @param {string} compressedData - 压缩的数据
     */
    async decompressData(compressedData) {
        return JSON.parse(compressedData);
    }

    /**
     * 加密数据
     * @param {*} data - 数据
     */
    async encryptData(data) {
        // 简单的Base64编码（实际项目中应使用真正的加密）
        return btoa(JSON.stringify(data));
    }

    /**
     * 解密数据
     * @param {string} encryptedData - 加密的数据
     */
    async decryptData(encryptedData) {
        return JSON.parse(atob(encryptedData));
    }

    /**
     * 处理存储变化
     * @param {Object} changes - 变化对象
     * @param {string} areaName - 存储区域名称
     */
    handleStorageChange(changes, areaName) {
        console.log(`💾 [StorageService] 存储变化检测: ${areaName}`, changes);

        // 更新缓存
        Object.keys(changes).forEach(key => {
            if (changes[key].newValue === undefined) {
                // 数据被删除
                this.removeFromCache(key);
            } else {
                // 数据被更新
                this.updateCache(key, changes[key].newValue);
            }
        });

        // 发布存储变化事件
        if (this.eventBus) {
            this.eventBus.emit('storage:changed', {
                changes: changes,
                area: areaName,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 处理设置请求
     * @param {Object} data - 请求数据
     */
    async handleSetRequest(data) {
        const { key, value, options, callback } = data;
        
        try {
            const result = await this.set(key, value, options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 处理获取请求
     * @param {Object} data - 请求数据
     */
    async handleGetRequest(data) {
        const { key, options, callback } = data;
        
        try {
            const result = await this.get(key, options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 处理移除请求
     * @param {Object} data - 请求数据
     */
    async handleRemoveRequest(data) {
        const { key, options, callback } = data;
        
        try {
            const result = await this.remove(key, options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 处理清除请求
     * @param {Object} data - 请求数据
     */
    async handleClearRequest(data) {
        const { options, callback } = data;
        
        try {
            const result = await this.clear(options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 获取存储统计
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            cacheHitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) || 0
        };
    }

    /**
     * 获取存储使用情况
     */
    async getUsage() {
        const usage = {};
        
        try {
            if (chrome.storage.sync) {
                usage.sync = await chrome.storage.sync.getBytesInUse();
            }
            if (chrome.storage.local) {
                usage.local = await chrome.storage.local.getBytesInUse();
            }
            if (chrome.storage.session) {
                usage.session = await chrome.storage.session.getBytesInUse();
            }
        } catch (error) {
            console.warn('⚠️ [StorageService] 无法获取存储使用情况', error);
        }
        
        return usage;
    }

    /**
     * 销毁存储服务
     */
    destroy() {
        // 清除缓存
        this.clearCache();
        
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('storage:set');
            this.eventBus.off('storage:get');
            this.eventBus.off('storage:remove');
            this.eventBus.off('storage:clear');
        }

        console.log('🗑️ [StorageService] 存储服务已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageService;
} else {
    window.StorageService = StorageService;
}

console.log('✅ [StorageService] 存储服务模块已加载');
