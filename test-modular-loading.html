<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC模块化加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 MDAC模块化架构加载测试</h1>
        <p>此页面用于测试模块化架构的加载和初始化过程</p>
        
        <div id="testResults">
            <div class="test-result info">
                <strong>📋 测试开始</strong><br>
                正在加载模块化架构...
            </div>
        </div>
        
        <h2>📊 加载状态</h2>
        <div id="loadingStatus">
            <div class="test-result warning">等待模块加载...</div>
        </div>
        
        <h2>🔍 模块检查</h2>
        <div id="moduleCheck">
            <div class="test-result warning">等待模块检查...</div>
        </div>
        
        <h2>📝 控制台日志</h2>
        <pre id="consoleLog">等待日志输出...</pre>
    </div>

    <!-- 模块化版本 - 按依赖顺序加载所有模块 -->
    
    <!-- 第一层：核心基础模块 -->
    <script src="ui/sidepanel/core/EventBus.js"></script>
    <script src="ui/sidepanel/core/StateManager.js"></script>
    <script src="ui/sidepanel/core/ModuleRegistry.js"></script>
    <script src="ui/sidepanel/core/ModuleLoader.js"></script>
    <script src="ui/sidepanel/core/EventManager.js"></script>
    <script src="ui/sidepanel/core/SidePanelCore.js"></script>
    <script src="ui/sidepanel/core/ModuleInitializer.js"></script>
    
    <!-- 第二层：工具模块 -->
    <script src="ui/sidepanel/utils/DateFormatter.js"></script>
    <script src="ui/sidepanel/utils/MessageHelper.js"></script>
    <script src="ui/sidepanel/utils/DebugLogger.js"></script>
    
    <!-- 第三层：数据管理模块 -->
    <script src="ui/sidepanel/data/StorageService.js"></script>
    <script src="ui/sidepanel/data/DataManager.js"></script>
    <script src="ui/sidepanel/data/PreviewManager.js"></script>
    
    <!-- 第四层：AI功能模块 -->
    <script src="ui/sidepanel/ai/AIService.js"></script>
    <script src="ui/sidepanel/ai/TextParser.js"></script>
    <script src="ui/sidepanel/ai/ImageProcessor.js"></script>
    
    <!-- 第五层：表单处理模块 -->
    <script src="ui/sidepanel/form/DataValidator.js"></script>
    <script src="ui/sidepanel/form/FieldMatcher.js"></script>
    <script src="ui/sidepanel/form/FormFiller.js"></script>
    
    <!-- 第六层：UI组件模块 -->
    <script src="ui/sidepanel/ui/ModalManager.js"></script>
    <script src="ui/sidepanel/ui/ProgressVisualizer.js"></script>
    <script src="ui/sidepanel/ui/UIRenderer.js"></script>
    
    <!-- 第七层：特色功能模块 -->
    <script src="ui/sidepanel/features/ConfidenceEvaluator.js"></script>
    <script src="ui/sidepanel/features/CityViewer.js"></script>
    <script src="ui/sidepanel/features/AutoParseManager.js"></script>
    
    <!-- 第八层：兼容性适配器 -->
    <script src="ui/sidepanel/compatibility/LegacyAdapter.js"></script>
    
    <!-- 第九层：配置和测试 -->
    <script src="ui/sidepanel/config/performance-config.js"></script>
    <script src="ui/sidepanel/tests/modular-integration-test.js"></script>
    
    <!-- 最后：主入口文件 -->
    <script src="ui/sidepanel/ui-sidepanel-modular.js"></script>

    <script>
        // 测试脚本
        let testResults = [];
        let consoleMessages = [];
        
        // 捕获控制台消息
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        ['log', 'error', 'warn', 'info'].forEach(method => {
            console[method] = function(...args) {
                originalConsole[method].apply(console, args);
                consoleMessages.push(`[${method.toUpperCase()}] ${args.join(' ')}`);
                updateConsoleLog();
            };
        });
        
        function updateConsoleLog() {
            const consoleLog = document.getElementById('consoleLog');
            consoleLog.textContent = consoleMessages.slice(-20).join('\n');
        }
        
        function addTestResult(type, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong><br>${message}`;
            resultsDiv.appendChild(resultDiv);
            testResults.push({type, message, timestamp: Date.now()});
        }
        
        function updateLoadingStatus(status) {
            const statusDiv = document.getElementById('loadingStatus');
            statusDiv.innerHTML = `<div class="test-result info">${status}</div>`;
        }
        
        function checkModules() {
            const moduleCheck = document.getElementById('moduleCheck');
            let checkResults = [];
            
            // 检查关键类是否已定义
            const requiredClasses = [
                'EventBus', 'EventManager', 'StateManager', 'ModuleRegistry',
                'ModuleLoader', 'SidePanelCore', 'ModuleInitializer',
                'DebugLogger', 'MessageHelper', 'DateFormatter',
                'StorageService', 'DataManager', 'PreviewManager',
                'MDACModularSidePanel'
            ];
            
            requiredClasses.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    checkResults.push(`✅ ${className} - 已加载`);
                } else {
                    checkResults.push(`❌ ${className} - 未找到`);
                }
            });
            
            // 检查全局对象
            if (window.mdacEventBus) {
                checkResults.push(`✅ mdacEventBus - 已创建`);
            } else {
                checkResults.push(`❌ mdacEventBus - 未创建`);
            }
            
            if (window.mdacModularSidePanel) {
                checkResults.push(`✅ mdacModularSidePanel - 已创建`);
                
                // 检查模块状态
                try {
                    const status = window.mdacModularSidePanel.getModuleStatus();
                    checkResults.push(`📊 模块状态: ${JSON.stringify(status, null, 2)}`);
                } catch (error) {
                    checkResults.push(`❌ 获取模块状态失败: ${error.message}`);
                }
            } else {
                checkResults.push(`❌ mdacModularSidePanel - 未创建`);
            }
            
            moduleCheck.innerHTML = checkResults.map(result => 
                `<div class="test-result ${result.startsWith('✅') ? 'success' : result.startsWith('❌') ? 'error' : 'info'}">${result}</div>`
            ).join('');
        }
        
        // 等待所有脚本加载完成
        window.addEventListener('load', function() {
            updateLoadingStatus('所有脚本已加载，开始检查模块...');
            
            setTimeout(() => {
                try {
                    checkModules();
                    
                    if (window.mdacModularSidePanel) {
                        addTestResult('success', '✅ 模块化架构加载成功！');
                        
                        // 尝试初始化
                        window.mdacModularSidePanel.initialize().then(() => {
                            addTestResult('success', '✅ 模块化架构初始化成功！');
                        }).catch(error => {
                            addTestResult('error', `❌ 初始化失败: ${error.message}`);
                        });
                        
                    } else {
                        addTestResult('error', '❌ 模块化架构加载失败');
                    }
                } catch (error) {
                    addTestResult('error', `❌ 测试过程中发生错误: ${error.message}`);
                }
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', function(event) {
            addTestResult('error', `❌ JavaScript错误: ${event.error.message} (${event.filename}:${event.lineno})`);
        });
        
        // 定期更新模块检查
        setInterval(checkModules, 5000);
    </script>
</body>
</html>
