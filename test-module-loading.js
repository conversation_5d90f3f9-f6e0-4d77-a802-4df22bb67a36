/**
 * MDAC Chrome扩展模块加载测试脚本
 * 用于验证修复后的模块加载功能
 * 创建日期: 2025-01-11
 */

// 测试配置
const TEST_CONFIG = {
    timeout: 30000, // 30秒超时
    expectedModules: [
        'MD<PERSON>Logger',
        'MDACDebugConsole', 
        'FormFieldDetector',
        'ErrorRecoveryManager',
        'FillMonitor',
        'ProgressVisualizer',
        'FieldStatusDisplay',
        'GoogleMapsIntegration'
    ],
    expectedGlobals: [
        'window.MDAC_AI_CONFIG',
        'window.mdacLogger',
        'window.GEMINI_CONFIG',
        'window.AI_PROMPTS'
    ]
};

class ModuleLoadingTester {
    constructor() {
        this.testResults = {
            startTime: Date.now(),
            modules: {},
            globals: {},
            errors: [],
            warnings: [],
            summary: {}
        };
        
        console.log('🧪 [测试] MDAC模块加载测试开始');
        this.runTests();
    }

    /**
     * 运行所有测试
     */
    async runTests() {
        try {
            // 等待页面完全加载
            await this.waitForPageReady();
            
            // 测试模块可用性
            this.testModuleAvailability();
            
            // 测试全局变量
            this.testGlobalVariables();
            
            // 测试模块功能
            await this.testModuleFunctionality();
            
            // 生成测试报告
            this.generateTestReport();
            
        } catch (error) {
            console.error('🔥 [测试] 测试过程中发生错误:', error);
            this.testResults.errors.push({
                type: 'TEST_ERROR',
                message: error.message,
                stack: error.stack,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 等待页面准备就绪
     */
    waitForPageReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    /**
     * 测试模块可用性
     */
    testModuleAvailability() {
        console.log('🔍 [测试] 检查模块可用性...');
        
        TEST_CONFIG.expectedModules.forEach(moduleName => {
            const isAvailable = typeof window[moduleName] !== 'undefined';
            this.testResults.modules[moduleName] = {
                available: isAvailable,
                type: typeof window[moduleName],
                timestamp: Date.now()
            };
            
            if (isAvailable) {
                console.log(`✅ [测试] 模块可用: ${moduleName}`);
            } else {
                console.warn(`⚠️ [测试] 模块不可用: ${moduleName}`);
                this.testResults.warnings.push({
                    type: 'MODULE_UNAVAILABLE',
                    module: moduleName,
                    timestamp: Date.now()
                });
            }
        });
    }

    /**
     * 测试全局变量
     */
    testGlobalVariables() {
        console.log('🔍 [测试] 检查全局变量...');
        
        TEST_CONFIG.expectedGlobals.forEach(globalPath => {
            const value = this.getNestedProperty(window, globalPath.replace('window.', ''));
            const isAvailable = value !== undefined;
            
            this.testResults.globals[globalPath] = {
                available: isAvailable,
                type: typeof value,
                hasContent: isAvailable && (typeof value === 'object' ? Object.keys(value).length > 0 : true),
                timestamp: Date.now()
            };
            
            if (isAvailable) {
                console.log(`✅ [测试] 全局变量可用: ${globalPath}`);
            } else {
                console.warn(`⚠️ [测试] 全局变量不可用: ${globalPath}`);
                this.testResults.warnings.push({
                    type: 'GLOBAL_UNAVAILABLE',
                    global: globalPath,
                    timestamp: Date.now()
                });
            }
        });
    }

    /**
     * 测试模块功能
     */
    async testModuleFunctionality() {
        console.log('🔍 [测试] 测试模块功能...');
        
        // 测试日志系统
        if (window.mdacLogger) {
            try {
                window.mdacLogger.info('TEST', '日志系统功能测试');
                console.log('✅ [测试] 日志系统功能正常');
            } catch (error) {
                console.error('❌ [测试] 日志系统功能异常:', error);
                this.testResults.errors.push({
                    type: 'LOGGER_ERROR',
                    message: error.message,
                    timestamp: Date.now()
                });
            }
        }
        
        // 测试字段检测器
        if (window.FormFieldDetector) {
            try {
                const detector = new FormFieldDetector();
                console.log('✅ [测试] 字段检测器实例化成功');
            } catch (error) {
                console.error('❌ [测试] 字段检测器实例化失败:', error);
                this.testResults.errors.push({
                    type: 'DETECTOR_ERROR',
                    message: error.message,
                    timestamp: Date.now()
                });
            }
        }
    }

    /**
     * 获取嵌套属性
     */
    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        const endTime = Date.now();
        const duration = endTime - this.testResults.startTime;
        
        // 计算统计信息
        const moduleStats = {
            total: TEST_CONFIG.expectedModules.length,
            available: Object.values(this.testResults.modules).filter(m => m.available).length
        };
        
        const globalStats = {
            total: TEST_CONFIG.expectedGlobals.length,
            available: Object.values(this.testResults.globals).filter(g => g.available).length
        };
        
        this.testResults.summary = {
            duration,
            moduleStats,
            globalStats,
            errorCount: this.testResults.errors.length,
            warningCount: this.testResults.warnings.length,
            overallSuccess: this.testResults.errors.length === 0 && 
                           moduleStats.available >= moduleStats.total * 0.8 &&
                           globalStats.available >= globalStats.total * 0.8
        };
        
        // 输出测试报告
        console.log('📊 [测试] ===== MDAC模块加载测试报告 =====');
        console.log(`⏱️ [测试] 测试耗时: ${duration}ms`);
        console.log(`📦 [测试] 模块状态: ${moduleStats.available}/${moduleStats.total} 可用`);
        console.log(`🌐 [测试] 全局变量: ${globalStats.available}/${globalStats.total} 可用`);
        console.log(`❌ [测试] 错误数量: ${this.testResults.errors.length}`);
        console.log(`⚠️ [测试] 警告数量: ${this.testResults.warnings.length}`);
        console.log(`🎯 [测试] 总体状态: ${this.testResults.summary.overallSuccess ? '✅ 成功' : '❌ 失败'}`);
        
        // 详细信息
        if (this.testResults.errors.length > 0) {
            console.log('🔍 [测试] 错误详情:');
            this.testResults.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. [${error.type}] ${error.message}`);
            });
        }
        
        if (this.testResults.warnings.length > 0) {
            console.log('🔍 [测试] 警告详情:');
            this.testResults.warnings.forEach((warning, index) => {
                console.log(`  ${index + 1}. [${warning.type}] ${warning.module || warning.global}`);
            });
        }
        
        // 将结果存储到全局变量供调试使用
        window.mdacTestResults = this.testResults;
        console.log('💾 [测试] 测试结果已保存到 window.mdacTestResults');
    }
}

// 自动运行测试（延迟3秒确保扩展加载完成）
setTimeout(() => {
    new ModuleLoadingTester();
}, 3000);

console.log('🧪 [测试] 模块加载测试脚本已加载，将在3秒后开始测试...');
