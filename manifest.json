{"manifest_version": 3, "name": "MDAC AI智能分析工具", "version": "2.0.0", "description": "基于Gemini AI的智能内容解析、数据验证和地址翻译工具 - 侧边栏版本", "permissions": ["sidePanel", "activeTab", "storage", "scripting", "tabs"], "host_permissions": ["https://imigresen-online.imi.gov.my/*", "https://generativelanguage.googleapis.com/*", "https://maps.googleapis.com/*"], "background": {"service_worker": "background/background.js"}, "side_panel": {"default_path": "ui/ui-sidepanel.html"}, "content_scripts": [{"matches": ["https://imigresen-online.imi.gov.my/*"], "js": ["content/content-script-adapter.js", "content/content-script.js"], "css": ["content/content-styles.css"], "run_at": "document_end"}], "action": {"default_title": "打开MDAC AI智能助手侧边栏", "default_icon": {"16": "assets/icons/icon16.png", "32": "assets/icons/icon32.png", "48": "assets/icons/icon48.png", "128": "assets/icons/icon128.png"}}, "icons": {"16": "assets/icons/icon16.png", "32": "assets/icons/icon32.png", "48": "assets/icons/icon48.png", "128": "assets/icons/icon128.png"}, "options_page": "ui/ui-options.html", "commands": {"fill-form": {"suggested_key": {"default": "Ctrl+Shift+F", "mac": "Command+Shift+F"}, "description": "AI智能填充MDAC表单"}, "open-side-panel": {"suggested_key": {"default": "Ctrl+Shift+S", "mac": "Command+Shift+S"}, "description": "打开MDAC AI侧边栏"}}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://generativelanguage.googleapis.com https://imigresen-online.imi.gov.my;"}, "web_accessible_resources": [{"resources": ["config/malaysia-states-cities.json", "config/ai-config.js", "config/enhanced-ai-config.js", "config/mdac-official-mappings.json", "ui/sidepanel/core/EventBus.js", "ui/sidepanel/core/ModuleLoader.js", "ui/sidepanel/core/ModuleRegistry.js", "ui/sidepanel/core/StateManager.js", "ui/sidepanel/core/EventManager.js", "ui/sidepanel/core/SidePanelCore.js", "ui/sidepanel/core/ModuleInitializer.js", "ui/sidepanel/utils/DateFormatter.js", "ui/sidepanel/utils/MessageHelper.js", "ui/sidepanel/utils/DebugLogger.js", "ui/sidepanel/ai/AIService.js", "ui/sidepanel/ai/TextParser.js", "ui/sidepanel/ai/ImageProcessor.js", "ui/sidepanel/form/FormFiller.js", "ui/sidepanel/form/FieldMatcher.js", "ui/sidepanel/form/DataValidator.js", "ui/sidepanel/ui/UIRenderer.js", "ui/sidepanel/ui/ModalManager.js", "ui/sidepanel/ui/ProgressVisualizer.js", "ui/sidepanel/features/AutoParseManager.js", "ui/sidepanel/features/ConfidenceEvaluator.js", "ui/sidepanel/features/CityViewer.js", "ui/sidepanel/data/DataManager.js", "ui/sidepanel/data/StorageService.js", "ui/sidepanel/data/PreviewManager.js", "ui/sidepanel/config/performance-config.js", "ui/sidepanel/compatibility/LegacyAdapter.js", "ui/sidepanel/tests/modular-integration-test.js", "ui/sidepanel/ui-sidepanel-modular.js"], "matches": ["https://imigresen-online.imi.gov.my/*"]}]}