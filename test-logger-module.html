<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logger模块测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #cce7ff;
            color: #004085;
            border: 1px solid #b8daff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #console-output {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Logger模块加载测试</h1>
        <p>测试 modules/logger.js 是否能正常加载和工作</p>
        
        <div id="test-results"></div>
        
        <button onclick="testLoggerModule()">测试Logger模块</button>
        <button onclick="testLoggerFunctions()">测试Logger功能</button>
        <button onclick="clearResults()">清除结果</button>
        
        <h3>控制台输出:</h3>
        <div id="console-output"></div>
    </div>

    <script>
        // 拦截console输出
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info,
            debug: console.debug
        };

        const consoleOutput = document.getElementById('console-output');
        
        function interceptConsole() {
            ['log', 'error', 'warn', 'info', 'debug'].forEach(method => {
                console[method] = function(...args) {
                    originalConsole[method].apply(console, args);
                    const message = args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' ');
                    consoleOutput.textContent += `[${method.toUpperCase()}] ${message}\n`;
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                };
            });
        }

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            consoleOutput.textContent = '';
        }

        async function testLoggerModule() {
            addResult('开始测试Logger模块加载...', 'info');
            
            try {
                // 测试加载logger.js脚本
                const script = document.createElement('script');
                script.src = 'modules/logger.js';
                
                script.onload = function() {
                    addResult('✅ modules/logger.js 脚本加载成功', 'success');
                    
                    // 测试MDACLogger类是否可用
                    if (typeof MDACLogger !== 'undefined') {
                        addResult('✅ MDACLogger 类定义成功', 'success');
                        
                        // 测试全局实例
                        if (window.mdacLogger) {
                            addResult('✅ 全局 window.mdacLogger 实例创建成功', 'success');
                        } else {
                            addResult('⚠️ 全局 window.mdacLogger 实例未找到', 'error');
                        }
                    } else {
                        addResult('❌ MDACLogger 类未定义', 'error');
                    }
                };
                
                script.onerror = function() {
                    addResult('❌ modules/logger.js 脚本加载失败', 'error');
                };
                
                document.head.appendChild(script);
            } catch (error) {
                addResult(`❌ 测试过程中出错: ${error.message}`, 'error');
            }
        }

        function testLoggerFunctions() {
            addResult('开始测试Logger功能...', 'info');
            
            try {
                if (!window.mdacLogger) {
                    addResult('❌ mdacLogger 实例不可用', 'error');
                    return;
                }
                
                const logger = window.mdacLogger;
                
                // 测试基本日志功能
                logger.debug('TEST', '这是一条DEBUG消息', { test: true });
                addResult('✅ debug() 方法调用成功', 'success');
                
                logger.info('TEST', '这是一条INFO消息', { test: true });
                addResult('✅ info() 方法调用成功', 'success');
                
                logger.warn('TEST', '这是一条WARN消息', { test: true });
                addResult('✅ warn() 方法调用成功', 'success');
                
                logger.error('TEST', '这是一条ERROR消息', { test: true });
                addResult('✅ error() 方法调用成功', 'success');
                
                // 测试性能监控
                logger.startPerformance('test-performance');
                setTimeout(() => {
                    const result = logger.endPerformance('test-performance');
                    addResult('✅ 性能监控功能测试成功', 'success');
                }, 100);
                
                // 测试日志获取
                const logs = logger.getLogs();
                if (Array.isArray(logs)) {
                    addResult(`✅ getLogs() 返回 ${logs.length} 条日志`, 'success');
                } else {
                    addResult('⚠️ getLogs() 返回格式异常', 'error');
                }
                
            } catch (error) {
                addResult(`❌ 功能测试失败: ${error.message}`, 'error');
            }
        }

        // 初始化
        interceptConsole();
        addResult('🚀 测试页面加载完成，点击按钮开始测试', 'info');
    </script>
</body>
</html>
