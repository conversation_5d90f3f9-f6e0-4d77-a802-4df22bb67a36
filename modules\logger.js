/**
 * 兼容性日志模块 - 为向后兼容而创建的包装器
 * 这是一个包装器，使用新的 DebugLogger 实现
 * 创建日期: 2025-01-11
 */

/**
 * MDAC日志记录器类 - 兼容性版本
 * 提供与旧版本相同的接口，但内部使用新的DebugLogger
 */
class MDACLogger {
    constructor() {
        // 如果新的DebugLogger可用，使用它
        if (typeof DebugLogger !== 'undefined') {
            this.debugLogger = new DebugLogger();
        } else {
            // 如果DebugLogger不可用，创建基本实现
            this.debugLogger = null;
            this.initBasicLogger();
        }
        
        // 设置全局实例
        if (!window.mdacLogger) {
            window.mdacLogger = this;
        }
    }

    /**
     * 初始化基本日志器（降级实现）
     */
    initBasicLogger() {
        this.logs = [];
        this.config = {
            enabled: true,
            level: 'INFO',
            maxLogs: 1000,
            enableConsole: true
        };
        
        this.levels = {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3
        };
    }

    /**
     * 通用日志方法
     */
    log(level, module, message, data = null) {
        if (this.debugLogger) {
            this.debugLogger.log(level, module, message, data);
        } else {
            this.basicLog(level, module, message, data);
        }
    }

    /**
     * 基本日志实现（降级）
     */
    basicLog(level, module, message, data) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            module,
            message,
            data
        };

        // 添加到内存日志
        this.logs.push(logEntry);
        if (this.logs.length > this.config.maxLogs) {
            this.logs.shift();
        }

        // 输出到控制台
        if (this.config.enableConsole) {
            const consoleMethod = level.toLowerCase();
            const prefix = `[${level}] ${module}:`;
            
            if (console[consoleMethod]) {
                if (data) {
                    console[consoleMethod](prefix, message, data);
                } else {
                    console[consoleMethod](prefix, message);
                }
            } else {
                console.log(prefix, message, data);
            }
        }
    }

    /**
     * DEBUG级别日志
     */
    debug(module, message, data = null) {
        this.log('DEBUG', module, message, data);
    }

    /**
     * INFO级别日志
     */
    info(module, message, data = null) {
        this.log('INFO', module, message, data);
    }

    /**
     * WARN级别日志
     */
    warn(module, message, data = null) {
        this.log('WARN', module, message, data);
    }

    /**
     * ERROR级别日志
     */
    error(module, message, data = null) {
        this.log('ERROR', module, message, data);
    }

    /**
     * 开始性能监控
     */
    startPerformance(label) {
        if (this.debugLogger && this.debugLogger.startPerformance) {
            this.debugLogger.startPerformance(label);
        } else {
            console.time(label);
        }
    }

    /**
     * 结束性能监控
     */
    endPerformance(label) {
        if (this.debugLogger && this.debugLogger.endPerformance) {
            return this.debugLogger.endPerformance(label);
        } else {
            console.timeEnd(label);
            return null;
        }
    }

    /**
     * 获取日志
     */
    getLogs(level = null, module = null) {
        if (this.debugLogger && this.debugLogger.getLogs) {
            return this.debugLogger.getLogs(level, module);
        } else {
            let filteredLogs = this.logs;
            
            if (level) {
                filteredLogs = filteredLogs.filter(log => log.level === level);
            }
            
            if (module) {
                filteredLogs = filteredLogs.filter(log => log.module === module);
            }
            
            return filteredLogs;
        }
    }

    /**
     * 清除日志
     */
    clearLogs() {
        if (this.debugLogger && this.debugLogger.clearLogs) {
            this.debugLogger.clearLogs();
        } else {
            this.logs = [];
        }
    }

    /**
     * 设置日志级别
     */
    setLevel(level) {
        if (this.debugLogger && this.debugLogger.setLevel) {
            this.debugLogger.setLevel(level);
        } else {
            this.config.level = level;
        }
    }

    /**
     * 输出到控制台方法（兼容性）
     */
    outputToConsole(level, module, message, data) {
        this.log(level, module, message, data);
    }
}

// 自动创建全局实例
if (typeof window !== 'undefined' && !window.mdacLogger) {
    try {
        window.mdacLogger = new MDACLogger();
        console.log('✅ [Logger] MDACLogger兼容性包装器已创建');
    } catch (error) {
        console.error('❌ [Logger] MDACLogger创建失败:', error);
    }
}

// 导出类（用于ES6模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MDACLogger;
}
