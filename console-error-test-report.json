{"timestamp": "2025-07-11T16:25:43.743Z", "summary": {"moduleConfigTests": 3, "moduleConfigPassed": 3, "domSelectorTests": 12, "domSelectorPassed": 12, "initializationTests": 3, "initializationPassed": 3, "totalErrors": 0}, "details": {"moduleConfigTests": [{"name": "SidePanelCore模块查找逻辑", "status": "PASS", "details": {"hasModuleRegistrySync": true, "hasModuleLoaderRegister": true, "hasConfigValidation": true}, "file": "ui/sidepanel/core/SidePanelCore.js"}, {"name": "ModularSidePanel初始化顺序", "status": "PASS", "details": {"hasCorrectOrder": true, "hasDOMReady": true, "hasGlobalAssignment": true}, "file": "ui/sidepanel/ui-sidepanel-modular.js"}, {"name": "ModuleRegistry配置完整性", "status": "PASS", "details": {"hasStateManagerConfig": true, "hasEventManagerConfig": true, "hasValidationMethod": true}, "file": "ui/sidepanel/core/ModuleRegistry.js"}], "domSelectorTests": [{"selector": "#personalInfoInput", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#travelInfoInput", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#imageUploadBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#imageInput", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#parsePersonalBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#parseTravelBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#updateToMDACBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#clearBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#clearAllBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#autoParsePersonalEnabled", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#autoParseTravel Enabled", "inEventManager": true, "inHTML": true, "status": "PASS"}, {"selector": "#debugConsoleBtn", "inEventManager": true, "inHTML": true, "status": "PASS"}], "initializationTests": [{"name": "DOM就绪检查机制", "status": "PASS", "details": {"hasWaitForDOMReady": true, "hasImplementation": true}}, {"name": "模块初始化顺序", "status": "PASS", "details": {"registryBeforeLoader": true, "stateManagerAfterRegistry": true}}, {"name": "错误处理机制", "status": "PASS", "details": {"hasErrorLogging": true, "hasGracefulDegradation": true}}], "errors": []}}