/**
 * MDAC Chrome扩展验证脚本
 * 验证模块化重构和清理工作的完整性
 * 创建日期: 2025-01-11
 */

const fs = require('fs');
const path = require('path');

class MDACValidationScript {
    constructor() {
        this.projectRoot = process.cwd();
        this.validationResults = {
            passed: [],
            failed: [],
            warnings: [],
            summary: {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                warningCount: 0
            }
        };

        console.log('🔍 [MDACValidationScript] 初始化验证脚本');
    }

    /**
     * 执行完整验证流程
     */
    async executeValidation() {
        try {
            console.log('🚀 [MDACValidationScript] 开始执行验证流程');

            // 1. 验证文件结构
            await this.validateFileStructure();

            // 2. 验证HTML文件更新
            await this.validateHTMLUpdates();

            // 3. 验证manifest.json配置
            await this.validateManifestConfig();

            // 4. 验证模块化架构
            await this.validateModularArchitecture();

            // 5. 验证兼容性适配器
            await this.validateCompatibilityAdapters();

            // 6. 验证依赖关系
            await this.validateDependencies();

            // 7. 生成验证报告
            this.generateValidationReport();

            console.log('✅ [MDACValidationScript] 验证流程完成');

        } catch (error) {
            console.error('❌ [MDACValidationScript] 验证流程失败', error);
            this.addFailure('validation_process', '验证流程执行失败', error.message);
        }
    }

    /**
     * 验证文件结构
     */
    async validateFileStructure() {
        console.log('📁 [MDACValidationScript] 验证文件结构');

        // 验证新模块化文件存在
        const requiredFiles = [
            'ui/sidepanel/ui-sidepanel-modular.js',
            'ui/sidepanel/compatibility/LegacyAdapter.js',
            'content/content-script-adapter.js',
            'ui/sidepanel/core/EventManager.js',
            'ui/sidepanel/core/StateManager.js',
            'ui/sidepanel/core/ModuleRegistry.js',
            'ui/sidepanel/core/ModuleLoader.js',
            'ui/sidepanel/core/SidePanelCore.js',
            'ui/sidepanel/core/ModuleInitializer.js'
        ];

        for (const file of requiredFiles) {
            if (this.fileExists(file)) {
                this.addSuccess('file_structure', `必需文件存在: ${file}`);
            } else {
                this.addFailure('file_structure', `必需文件缺失: ${file}`);
            }
        }

        // 验证重复文件已删除
        // 注意: modules/logger.js 现在是兼容性包装器，不应删除
        const shouldNotExist = [
            'modules/confidence-evaluator.js',
            'modules/progress-visualizer.js',
            'modules/debug-console.js',
            'modules/data-preview-manager.js'
        ];

        for (const file of shouldNotExist) {
            if (!this.fileExists(file)) {
                this.addSuccess('file_cleanup', `重复文件已删除: ${file}`);
            } else {
                this.addFailure('file_cleanup', `重复文件仍存在: ${file}`);
            }
        }
    }

    /**
     * 验证HTML文件更新
     */
    async validateHTMLUpdates() {
        console.log('📄 [MDACValidationScript] 验证HTML文件更新');

        const htmlFile = 'ui/ui-sidepanel.html';
        
        if (!this.fileExists(htmlFile)) {
            this.addFailure('html_validation', 'HTML文件不存在');
            return;
        }

        const content = fs.readFileSync(path.join(this.projectRoot, htmlFile), 'utf8');

        // 检查是否使用新的模块化入口
        if (content.includes('sidepanel/ui-sidepanel-modular.js')) {
            this.addSuccess('html_validation', 'HTML文件已更新为使用模块化入口');
        } else {
            this.addFailure('html_validation', 'HTML文件未使用模块化入口');
        }

        // 检查是否还有旧模块引用
        const oldModulePatterns = [
            '../modules/logger.js',
            '../modules/debug-console.js',
            '../modules/confidence-evaluator.js',
            'ui-sidepanel.js'
        ];

        let hasOldReferences = false;
        for (const pattern of oldModulePatterns) {
            if (content.includes(pattern)) {
                this.addFailure('html_validation', `HTML文件仍包含旧模块引用: ${pattern}`);
                hasOldReferences = true;
            }
        }

        if (!hasOldReferences) {
            this.addSuccess('html_validation', 'HTML文件已清除所有旧模块引用');
        }
    }

    /**
     * 验证manifest.json配置
     */
    async validateManifestConfig() {
        console.log('📋 [MDACValidationScript] 验证manifest.json配置');

        const manifestFile = 'manifest.json';
        
        if (!this.fileExists(manifestFile)) {
            this.addFailure('manifest_validation', 'manifest.json文件不存在');
            return;
        }

        const manifest = JSON.parse(fs.readFileSync(path.join(this.projectRoot, manifestFile), 'utf8'));

        // 验证content_scripts配置
        if (manifest.content_scripts && manifest.content_scripts[0]) {
            const contentScript = manifest.content_scripts[0];
            
            if (contentScript.js && contentScript.js.includes('content/content-script-adapter.js')) {
                this.addSuccess('manifest_validation', 'manifest.json包含content-script适配器');
            } else {
                this.addFailure('manifest_validation', 'manifest.json缺少content-script适配器');
            }
        }

        // 验证web_accessible_resources配置
        if (manifest.web_accessible_resources) {
            const resources = manifest.web_accessible_resources[0];
            
            if (resources && resources.resources) {
                const hasModularFile = resources.resources.some(file => 
                    file.includes('ui-sidepanel-modular.js')
                );
                
                if (hasModularFile) {
                    this.addSuccess('manifest_validation', 'manifest.json包含模块化主文件');
                } else {
                    this.addWarning('manifest_validation', 'manifest.json可能缺少模块化主文件引用');
                }
            }
        }
    }

    /**
     * 验证模块化架构
     */
    async validateModularArchitecture() {
        console.log('🏗️ [MDACValidationScript] 验证模块化架构');

        const modularFile = 'ui/sidepanel/ui-sidepanel-modular.js';
        
        if (!this.fileExists(modularFile)) {
            this.addFailure('modular_architecture', '模块化主文件不存在');
            return;
        }

        const content = fs.readFileSync(path.join(this.projectRoot, modularFile), 'utf8');

        // 检查关键类和方法
        const requiredElements = [
            'class MDACModularSidePanel',
            'initializeCoreModules',
            'initializeUtilityModules',
            'initializeDataModules',
            'initializeFunctionalModules',
            'initializeUIModules',
            'initializeLegacyAdapter'
        ];

        for (const element of requiredElements) {
            if (content.includes(element)) {
                this.addSuccess('modular_architecture', `模块化架构包含: ${element}`);
            } else {
                this.addFailure('modular_architecture', `模块化架构缺少: ${element}`);
            }
        }

        // 检查模块实例定义
        const moduleInstances = [
            'eventManager',
            'stateManager',
            'debugLogger',
            'legacyAdapter'
        ];

        for (const instance of moduleInstances) {
            if (content.includes(instance + ':')) {
                this.addSuccess('modular_architecture', `模块实例已定义: ${instance}`);
            } else {
                this.addFailure('modular_architecture', `模块实例未定义: ${instance}`);
            }
        }
    }

    /**
     * 验证兼容性适配器
     */
    async validateCompatibilityAdapters() {
        console.log('🔄 [MDACValidationScript] 验证兼容性适配器');

        // 验证LegacyAdapter
        const legacyAdapterFile = 'ui/sidepanel/compatibility/LegacyAdapter.js';
        
        if (this.fileExists(legacyAdapterFile)) {
            this.addSuccess('compatibility', 'LegacyAdapter文件存在');
            
            const content = fs.readFileSync(path.join(this.projectRoot, legacyAdapterFile), 'utf8');
            
            // 检查关键功能
            const requiredFeatures = [
                'class LegacyAdapter',
                'moduleMapping',
                'apiMapping',
                'eventMapping',
                'createLegacyInstances'
            ];

            for (const feature of requiredFeatures) {
                if (content.includes(feature)) {
                    this.addSuccess('compatibility', `LegacyAdapter包含: ${feature}`);
                } else {
                    this.addFailure('compatibility', `LegacyAdapter缺少: ${feature}`);
                }
            }
        } else {
            this.addFailure('compatibility', 'LegacyAdapter文件不存在');
        }

        // 验证ContentScriptAdapter
        const contentAdapterFile = 'content/content-script-adapter.js';
        
        if (this.fileExists(contentAdapterFile)) {
            this.addSuccess('compatibility', 'ContentScriptAdapter文件存在');
            
            const content = fs.readFileSync(path.join(this.projectRoot, contentAdapterFile), 'utf8');
            
            // 检查关键类
            const requiredClasses = [
                'class MDACLogger',
                'class MDACDebugConsole',
                'class FormFieldDetector',
                'class ErrorRecoveryManager'
            ];

            for (const className of requiredClasses) {
                if (content.includes(className)) {
                    this.addSuccess('compatibility', `ContentScriptAdapter包含: ${className}`);
                } else {
                    this.addFailure('compatibility', `ContentScriptAdapter缺少: ${className}`);
                }
            }
        } else {
            this.addFailure('compatibility', 'ContentScriptAdapter文件不存在');
        }
    }

    /**
     * 验证依赖关系
     */
    async validateDependencies() {
        console.log('🔗 [MDACValidationScript] 验证依赖关系');

        // 检查核心模块文件
        const coreModules = [
            'ui/sidepanel/core/EventManager.js',
            'ui/sidepanel/core/StateManager.js',
            'ui/sidepanel/core/ModuleRegistry.js'
        ];

        let coreModulesExist = true;
        for (const module of coreModules) {
            if (!this.fileExists(module)) {
                this.addFailure('dependencies', `核心模块缺失: ${module}`);
                coreModulesExist = false;
            }
        }

        if (coreModulesExist) {
            this.addSuccess('dependencies', '所有核心模块文件存在');
        }

        // 检查功能模块文件
        const functionalModules = [
            'ui/sidepanel/ai/AIService.js',
            'ui/sidepanel/form/FormFiller.js',
            'ui/sidepanel/data/DataManager.js'
        ];

        let functionalModulesExist = true;
        for (const module of functionalModules) {
            if (!this.fileExists(module)) {
                this.addFailure('dependencies', `功能模块缺失: ${module}`);
                functionalModulesExist = false;
            }
        }

        if (functionalModulesExist) {
            this.addSuccess('dependencies', '所有功能模块文件存在');
        }
    }

    /**
     * 检查文件是否存在
     */
    fileExists(filePath) {
        return fs.existsSync(path.join(this.projectRoot, filePath));
    }

    /**
     * 添加成功结果
     */
    addSuccess(category, message) {
        this.validationResults.passed.push({
            category: category,
            message: message,
            timestamp: Date.now()
        });
        this.validationResults.summary.totalTests++;
        this.validationResults.summary.passedTests++;
    }

    /**
     * 添加失败结果
     */
    addFailure(category, message, details = null) {
        this.validationResults.failed.push({
            category: category,
            message: message,
            details: details,
            timestamp: Date.now()
        });
        this.validationResults.summary.totalTests++;
        this.validationResults.summary.failedTests++;
    }

    /**
     * 添加警告
     */
    addWarning(category, message) {
        this.validationResults.warnings.push({
            category: category,
            message: message,
            timestamp: Date.now()
        });
        this.validationResults.summary.warningCount++;
    }

    /**
     * 生成验证报告
     */
    generateValidationReport() {
        const reportPath = path.join(this.projectRoot, 'validation-report.json');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.validationResults.summary,
            results: this.validationResults
        };

        // 计算成功率
        const successRate = this.validationResults.summary.totalTests > 0 
            ? (this.validationResults.summary.passedTests / this.validationResults.summary.totalTests * 100).toFixed(2)
            : 0;

        report.summary.successRate = successRate + '%';

        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('📊 [MDACValidationScript] 验证报告已生成:', reportPath);
        console.log('📈 验证统计:', report.summary);

        // 显示结果摘要
        console.log('\n🎯 验证结果摘要:');
        console.log(`✅ 通过: ${this.validationResults.summary.passedTests}`);
        console.log(`❌ 失败: ${this.validationResults.summary.failedTests}`);
        console.log(`⚠️ 警告: ${this.validationResults.summary.warningCount}`);
        console.log(`📊 成功率: ${successRate}%`);

        // 如果有失败项，显示详情
        if (this.validationResults.summary.failedTests > 0) {
            console.log('\n❌ 失败项详情:');
            this.validationResults.failed.forEach(failure => {
                console.log(`  - [${failure.category}] ${failure.message}`);
                if (failure.details) {
                    console.log(`    详情: ${failure.details}`);
                }
            });
        }

        // 如果有警告，显示详情
        if (this.validationResults.summary.warningCount > 0) {
            console.log('\n⚠️ 警告项详情:');
            this.validationResults.warnings.forEach(warning => {
                console.log(`  - [${warning.category}] ${warning.message}`);
            });
        }

        // 总体评估
        if (this.validationResults.summary.failedTests === 0) {
            console.log('\n🎉 验证通过！模块化重构和清理工作已成功完成。');
        } else if (this.validationResults.summary.failedTests <= 2) {
            console.log('\n⚠️ 验证基本通过，但有少量问题需要修复。');
        } else {
            console.log('\n❌ 验证失败，需要修复多个问题后重新验证。');
        }

        return report;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const validation = new MDACValidationScript();
    validation.executeValidation().then(() => {
        console.log('🎉 验证脚本执行完成');
        process.exit(0);
    }).catch(error => {
        console.error('💥 验证脚本执行失败', error);
        process.exit(1);
    });
}

module.exports = MDACValidationScript;
