---
type: "manual"
---

<div id="errorsList">
        <!--?lit$*********$--><!---->
          <div class="item-container">
            <div class="cr-row error-item selected">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="true">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="206">
                  <!--?lit$*********$-->❌ [ContentScript] 模块加载失败: chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/modules/debug-console.js [object Event]
                </div>
                <div class="cr-icon icon-expand-less">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="206" aria-describedby="206" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="false" class="collapse-opened" opened="" style="">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://imigresen-online.imi.gov.my/mdac/main?registerMain
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="0">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="0" tabindex="0" class="selected">
                        <!--?lit$*********$-->content/content-script.js:410 (script.onerror)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along." is-active="">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="207">
                  <!--?lit$*********$-->🔥 [ContentScript] 分层加载模块时发生错误: Error: 模块加载失败: chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/modules/debug-console.js
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="207" aria-describedby="207" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://imigresen-online.imi.gov.my/mdac/main?registerMain
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->content/content-script.js:227 (loadAllModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="208">
                  <!--?lit$*********$-->💡 [ContentScript] 错误详情: [object Object]
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="208" aria-describedby="208" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://imigresen-online.imi.gov.my/mdac/main?registerMain
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$*********$-->content/content-script.js:228 (loadAllModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="209">
                  <!--?lit$*********$-->❌ [ContentScript] 初始化过程中发生严重错误: Error: 模块加载失败: chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/modules/debug-console.js
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="209" aria-describedby="209" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://imigresen-online.imi.gov.my/mdac/main?registerMain
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$*********$-->content/content-script.js:132 (setup)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="210">
                  <!--?lit$*********$-->🔍 [ContentScript] 错误详细信息: [object Object]
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="210" aria-describedby="210" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://imigresen-online.imi.gov.my/mdac/main?registerMain
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$*********$-->content/content-script.js:133 (setup)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="211">
                  <!--?lit$*********$-->Uncaught SyntaxError: Unexpected end of input
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="211" aria-describedby="211" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/data/PreviewManager.js:824 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="6" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="212">
                  <!--?lit$*********$-->Uncaught SyntaxError: Unexpected end of input
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="212" aria-describedby="212" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="6">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui/UIRenderer.js:731 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="7" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="213">
                  <!--?lit$*********$-->Uncaught SyntaxError: Unexpected eval or arguments in strict mode
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="213" aria-describedby="213" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="7">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/features/ConfidenceEvaluator.js:831 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="8" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="214">
                  <!--?lit$*********$-->Uncaught SyntaxError: Unexpected end of input
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="214" aria-describedby="214" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="8">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/features/CityViewer.js:841 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="9" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="216">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #image-upload
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="216" aria-describedby="216" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="9">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:83 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="10" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="217">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #parse-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="217" aria-describedby="217" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="10">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:88 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="11" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="218">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #fill-form-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="218" aria-describedby="218" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="11">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:92 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="12" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="219">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #clear-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="219" aria-describedby="219" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="12">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:96 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="13" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="220">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: .tab-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="220" aria-describedby="220" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="13">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:101 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="14" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="221">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #auto-parse-toggle
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="221" aria-describedby="221" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="14">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:106 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="15" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="222">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #debug-toggle
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="222" aria-describedby="222" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="15">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:110 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="16" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="215">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #input-text
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="215" aria-describedby="215" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="16">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:238 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:125 (initializeDOMEvents)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:42 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:168 (initializeCoreModules)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:102 (initialize)
                      </li><!----><!---->
                      <li data-frame-index="5" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:83 (MDACModularSidePanel)
                      </li><!----><!---->
                      <li data-frame-index="6" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:678 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="17" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="223">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 核心模块初始化失败 TypeError: this.modules.stateManager.initialize is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="223" aria-describedby="223" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="17">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="17" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:193 (initializeCoreModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="18" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="224">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 初始化失败 TypeError: this.modules.stateManager.initialize is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="224" aria-describedby="224" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="18">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="18" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:146 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="19" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="225">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 初始化错误处理 TypeError: this.modules.stateManager.initialize is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="225" aria-describedby="225" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="19">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="19" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:566 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="19" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:148 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="20" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="226">
                  <!--?lit$*********$-->⚠️ [MDACModularSidePanel] 进入降级模式
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="226" aria-describedby="226" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="20">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="20" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:576 (enterDegradedMode)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="20" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:569 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="20" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:148 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="21" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="227">
                  <!--?lit$*********$-->Uncaught (in promise) TypeError: this.modules.stateManager.initialize is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="227" aria-describedby="227" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="21">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="21" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:172 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>