---
type: "manual"
---

<div id="errorsList">
        <!--?lit$*********$--><!---->
          <div class="item-container">
            <div class="cr-row error-item selected">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="true">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="250">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'EventBus' has already been declared
                </div>
                <div class="cr-icon icon-expand-less">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="250" aria-describedby="250" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="false" class="collapse-opened" opened="" style="">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="0">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="0" tabindex="0" class="selected">
                        <!--?lit$*********$-->ui/sidepanel/core/EventBus.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along." is-active="">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="251">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #autoParseTravel Enabled
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="251" aria-describedby="251" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:282 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/EventManager.js:129 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="252">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DebugLogger' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="252" aria-describedby="252" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/utils/DebugLogger.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="253">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DateFormatter' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="253" aria-describedby="253" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/utils/DateFormatter.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="254">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'MessageHelper' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="254" aria-describedby="254" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/utils/MessageHelper.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="255">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'StorageService' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="255" aria-describedby="255" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/data/StorageService.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="6" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="256">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DataValidator' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="256" aria-describedby="256" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="6">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/form/DataValidator.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="7" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="257">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'ModalManager' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="257" aria-describedby="257" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="7">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui/ModalManager.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="8" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="258">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DataManager' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="258" aria-describedby="258" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="8">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/data/DataManager.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="9" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="259">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'FieldMatcher' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="259" aria-describedby="259" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="9">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/form/FieldMatcher.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="10" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="260">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'AIService' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="260" aria-describedby="260" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="10">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ai/AIService.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="11" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="261">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'UIRenderer' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="261" aria-describedby="261" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="11">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui/UIRenderer.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="12" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="262">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'FormFiller' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="262" aria-describedby="262" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="12">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/form/FormFiller.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="13" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="263">
                  <!--?lit$*********$-->❌ [AIService] AI服务初始化失败 Error: API密钥未配置
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="263" aria-describedby="263" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="13">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ai/AIService.js:70 (initializeService)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="14" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="264">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'CityViewer' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="264" aria-describedby="264" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="14">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/features/CityViewer.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="15" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="265">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'TextParser' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="265" aria-describedby="265" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="15">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ai/TextParser.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="16" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="266">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'ConfidenceEvaluator' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="266" aria-describedby="266" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="16">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/features/ConfidenceEvaluator.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="17" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="267">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'AutoParseManager' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="267" aria-describedby="267" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="17">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="17" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/features/AutoParseManager.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="18" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="268">
                  <!--?lit$*********$-->❌ [UIRenderer] UI初始化失败 TypeError: this.initializeTabSwitching is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="268" aria-describedby="268" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="18">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="18" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui/UIRenderer.js:78 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="19" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="269">
                  <!--?lit$*********$-->❌ [SidePanelCore] 侧边栏初始化失败 TypeError: this.initializeTabSwitching is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="269" aria-describedby="269" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="19">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="19" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/core/SidePanelCore.js:94 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="20" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="270">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 核心模块初始化失败 TypeError: this.initializeTabSwitching is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="270" aria-describedby="270" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="20">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="20" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:219 (initializeCoreModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="21" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="271">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 初始化失败 TypeError: this.initializeTabSwitching is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="271" aria-describedby="271" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="21">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="21" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:167 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="22" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="272">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 初始化错误处理 TypeError: this.initializeTabSwitching is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="272" aria-describedby="272" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="22">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="22" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:592 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="22" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:169 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="23" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="273">
                  <!--?lit$*********$-->⚠️ [MDACModularSidePanel] 进入降级模式
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="273" aria-describedby="273" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="23">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="23" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:602 (enterDegradedMode)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="23" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:595 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="23" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:169 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="24" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="274">
                  <!--?lit$*********$-->Uncaught (in promise) TypeError: this.initializeTabSwitching is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="274" aria-describedby="274" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="24">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="24" tabindex="-1" class="">
                        <!--?lit$*********$-->ui/sidepanel/ui-sidepanel-modular.js:172 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>