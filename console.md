---
type: "manual"
---

<div id="errorsList">
        <!--?lit$*********$--><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="300">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'EventBus' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="300" aria-describedby="300" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="0">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="0" tabindex="0" class="selected">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventBus.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="301">
                  <!--?lit$*********$-->⚠️ [EventManager] DOM元素未找到: #autoParseTravel Enabled
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="301" aria-describedby="301" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:282 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:129 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="302">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DebugLogger' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="302" aria-describedby="302" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/utils/DebugLogger.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="303">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DateFormatter' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="303" aria-describedby="303" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/utils/DateFormatter.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="304">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'MessageHelper' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="304" aria-describedby="304" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/utils/MessageHelper.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="305">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'StorageService' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="305" aria-describedby="305" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/data/StorageService.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="6" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="306">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DataValidator' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="306" aria-describedby="306" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="6">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/form/DataValidator.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="7" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="307">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'ModalManager' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="307" aria-describedby="307" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="7">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/ModalManager.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="8" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="308">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'AIService' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="308" aria-describedby="308" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="8">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ai/AIService.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="9" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="309">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'DataManager' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="309" aria-describedby="309" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="9">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/data/DataManager.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="10" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="310">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'FieldMatcher' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="310" aria-describedby="310" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="10">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/form/FieldMatcher.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="11" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="311">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'UIRenderer' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="311" aria-describedby="311" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="11">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/UIRenderer.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="12" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="312">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'FormFiller' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="312" aria-describedby="312" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="12">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/form/FormFiller.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="13" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="313">
                  <!--?lit$*********$-->❌ [AIService] AI服务初始化失败 Error: API密钥未配置
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="313" aria-describedby="313" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="13">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ai/AIService.js:70 (initializeService)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="14" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="314">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'TextParser' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="314" aria-describedby="314" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="14">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="14" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ai/TextParser.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="15" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="315">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'CityViewer' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="315" aria-describedby="315" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="15">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="15" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/features/CityViewer.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="16" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="316">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'ConfidenceEvaluator' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="316" aria-describedby="316" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="16">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="16" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/features/ConfidenceEvaluator.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="17" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="317">
                  <!--?lit$*********$-->Uncaught SyntaxError: Identifier 'AutoParseManager' has already been declared
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="317" aria-describedby="317" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="17">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="17" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/features/AutoParseManager.js:1 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="18" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="318">
                  <!--?lit$*********$-->❌ [UIRenderer] UI初始化失败 TypeError: this.initializeSettings is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="318" aria-describedby="318" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="18">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="18" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/UIRenderer.js:78 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="19" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="319">
                  <!--?lit$*********$-->❌ [SidePanelCore] 侧边栏初始化失败 TypeError: this.initializeSettings is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="319" aria-describedby="319" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="19">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="19" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/SidePanelCore.js:94 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="20" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="320">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 核心模块初始化失败 TypeError: this.initializeSettings is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="320" aria-describedby="320" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="20">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="20" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:219 (initializeCoreModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="21" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="321">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 初始化失败 TypeError: this.initializeSettings is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="321" aria-describedby="321" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="21">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="21" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:167 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="22" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="322">
                  <!--?lit$*********$-->❌ [MDACModularSidePanel] 初始化错误处理 TypeError: this.initializeSettings is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="322" aria-describedby="322" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="22">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="22" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:592 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="22" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:169 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="23" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="323">
                  <!--?lit$*********$-->⚠️ [MDACModularSidePanel] 进入降级模式
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="323" aria-describedby="323" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="23">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="23" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:602 (enterDegradedMode)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="23" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:595 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="23" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:169 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="24" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="324">
                  <!--?lit$*********$-->Uncaught (in promise) TypeError: this.initializeSettings is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="324" aria-describedby="324" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="24">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="24" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:172 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="25" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="325">
                  <!--?lit$*********$-->❌ [EventBus] 事件处理器执行异常: state:changed TypeError: this.handleStateChange is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="325" aria-describedby="325" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="25">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="25" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventBus.js:155 (executeHandler)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="25" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventBus.js:128 (emit)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="25" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/StateManager.js:154 (set)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="25" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/UIRenderer.js:799 (switchTab)
                      </li><!----><!---->
                      <li data-frame-index="4" data-error-index="25" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/UIRenderer.js:754 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="26" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="326">
                  <!--?lit$*********$-->❌ [EventBus] 事件处理器执行失败: state:changed TypeError: this.handleStateChange is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="326" aria-describedby="326" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="26">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="26" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventBus.js:131 (emit)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="26" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/StateManager.js:154 (set)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="26" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/UIRenderer.js:799 (switchTab)
                      </li><!----><!---->
                      <li data-frame-index="3" data-error-index="26" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui/UIRenderer.js:754 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>