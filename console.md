---
type: "manual"
---

<div id="errorsList">
        <!--?lit$976035336$--><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="234">
                  <!--?lit$976035336$-->❌ [SidePanelCore] 侧边栏初始化失败 Error: 模块配置未找到: StateManager
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="234" aria-describedby="234" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="0">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="0" tabindex="0" class="selected">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/SidePanelCore.js:94 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="235">
                  <!--?lit$976035336$-->❌ [MDACModularSidePanel] 核心模块初始化失败 Error: 模块配置未找到: StateManager
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="235" aria-describedby="235" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:193 (initializeCoreModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="236">
                  <!--?lit$976035336$-->❌ [MDACModularSidePanel] 初始化失败 Error: 模块配置未找到: StateManager
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="236" aria-describedby="236" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:146 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="237">
                  <!--?lit$976035336$-->❌ [MDACModularSidePanel] 初始化错误处理 Error: 模块配置未找到: StateManager
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="237" aria-describedby="237" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:566 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:148 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="238">
                  <!--?lit$976035336$-->⚠️ [MDACModularSidePanel] 进入降级模式
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="238" aria-describedby="238" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:576 (enterDegradedMode)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:569 (handleInitializationError)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:148 (initialize)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="239">
                  <!--?lit$976035336$-->Uncaught (in promise) Error: 模块配置未找到: StateManager
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="239" aria-describedby="239" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/ui-sidepanel-modular.js:151 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="6" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="241">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #image-upload
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="241" aria-describedby="241" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="6">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:85 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="7" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="242">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #parse-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="242" aria-describedby="242" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="7">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:90 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="8" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="243">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #fill-form-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="243" aria-describedby="243" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="8">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:94 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="9" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="244">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #clear-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="244" aria-describedby="244" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="9">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:98 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="10" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="245">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: .tab-button
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="245" aria-describedby="245" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="10">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:103 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="11" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="246">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #auto-parse-toggle
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="246" aria-describedby="246" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="11">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="11" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:108 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="12" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="247">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #debug-toggle
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="247" aria-describedby="247" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="12">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="12" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:112 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="13" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="240">
                  <!--?lit$976035336$-->⚠️ [EventManager] DOM元素未找到: #input-text
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="240" aria-describedby="240" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$976035336$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$976035336$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="13">
                    <!--?lit$976035336$--><!---->
                      <li data-frame-index="0" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:249 (addDOMListener)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="13" tabindex="-1" class="">
                        <!--?lit$976035336$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/sidepanel/core/EventManager.js:127 (bindDOMEvents)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>