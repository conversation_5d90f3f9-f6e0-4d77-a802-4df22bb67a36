/**
 * MDAC Chrome扩展 - Manifest文件引用检查器
 * 专门检查manifest.json中web_accessible_resources的文件引用完整性
 */

const fs = require('fs');
const path = require('path');

class ManifestReferenceChecker {
    constructor() {
        this.projectRoot = __dirname;
        this.checkResults = {
            existingFiles: [],
            missingFiles: [],
            duplicateReferences: [],
            unusedFiles: [],
            errors: []
        };
    }

    /**
     * 执行完整的引用检查
     */
    async runCheck() {
        console.log('🔍 [ManifestReferenceChecker] 开始检查manifest.json文件引用');
        
        try {
            // 1. 检查manifest.json是否存在
            await this.checkManifestExists();
            
            // 2. 解析manifest.json
            const manifest = await this.parseManifest();
            
            // 3. 检查web_accessible_resources中的文件
            await this.checkWebAccessibleResources(manifest);
            
            // 4. 检查重复引用
            await this.checkDuplicateReferences(manifest);
            
            // 5. 生成报告
            await this.generateReport();
            
        } catch (error) {
            console.error('❌ [ManifestReferenceChecker] 检查过程中发生错误:', error);
            this.checkResults.errors.push({
                type: 'fatal_error',
                message: error.message,
                stack: error.stack
            });
        }
    }

    /**
     * 检查manifest.json是否存在
     */
    async checkManifestExists() {
        const manifestPath = path.join(this.projectRoot, 'manifest.json');
        
        if (!fs.existsSync(manifestPath)) {
            throw new Error('manifest.json文件不存在');
        }
        
        console.log('✅ [ManifestReferenceChecker] manifest.json文件存在');
    }

    /**
     * 解析manifest.json
     */
    async parseManifest() {
        const manifestPath = path.join(this.projectRoot, 'manifest.json');
        
        try {
            const content = fs.readFileSync(manifestPath, 'utf8');
            const manifest = JSON.parse(content);
            console.log('✅ [ManifestReferenceChecker] manifest.json解析成功');
            return manifest;
        } catch (error) {
            throw new Error(`manifest.json解析失败: ${error.message}`);
        }
    }

    /**
     * 检查web_accessible_resources中的文件
     */
    async checkWebAccessibleResources(manifest) {
        console.log('🔍 [ManifestReferenceChecker] 检查web_accessible_resources');
        
        if (!manifest.web_accessible_resources || !Array.isArray(manifest.web_accessible_resources)) {
            this.checkResults.errors.push({
                type: 'config_error',
                message: 'web_accessible_resources配置不存在或格式错误'
            });
            return;
        }

        for (const resourceGroup of manifest.web_accessible_resources) {
            if (resourceGroup.resources && Array.isArray(resourceGroup.resources)) {
                for (const filePath of resourceGroup.resources) {
                    await this.checkSingleFile(filePath);
                }
            }
        }
    }

    /**
     * 检查单个文件
     */
    async checkSingleFile(filePath) {
        const fullPath = path.join(this.projectRoot, filePath);
        
        if (fs.existsSync(fullPath)) {
            this.checkResults.existingFiles.push({
                path: filePath,
                fullPath: fullPath,
                size: fs.statSync(fullPath).size
            });
            console.log(`✅ [ManifestReferenceChecker] 文件存在: ${filePath}`);
        } else {
            this.checkResults.missingFiles.push({
                path: filePath,
                fullPath: fullPath,
                reason: '文件不存在'
            });
            console.log(`❌ [ManifestReferenceChecker] 文件缺失: ${filePath}`);
        }
    }

    /**
     * 检查重复引用
     */
    async checkDuplicateReferences(manifest) {
        console.log('🔍 [ManifestReferenceChecker] 检查重复引用');
        
        const allResources = [];
        const resourceCounts = {};

        // 收集所有资源路径
        for (const resourceGroup of manifest.web_accessible_resources) {
            if (resourceGroup.resources && Array.isArray(resourceGroup.resources)) {
                allResources.push(...resourceGroup.resources);
            }
        }

        // 统计每个资源的出现次数
        for (const resource of allResources) {
            resourceCounts[resource] = (resourceCounts[resource] || 0) + 1;
        }

        // 找出重复的资源
        for (const [resource, count] of Object.entries(resourceCounts)) {
            if (count > 1) {
                this.checkResults.duplicateReferences.push({
                    path: resource,
                    count: count,
                    message: `资源被重复引用${count}次`
                });
                console.log(`⚠️ [ManifestReferenceChecker] 重复引用: ${resource} (${count}次)`);
            }
        }
    }

    /**
     * 生成检查报告
     */
    async generateReport() {
        console.log('📊 [ManifestReferenceChecker] 生成检查报告');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalFiles: this.checkResults.existingFiles.length + this.checkResults.missingFiles.length,
                existingFiles: this.checkResults.existingFiles.length,
                missingFiles: this.checkResults.missingFiles.length,
                duplicateReferences: this.checkResults.duplicateReferences.length,
                errors: this.checkResults.errors.length
            },
            details: this.checkResults
        };

        // 保存报告到文件
        const reportPath = path.join(this.projectRoot, 'manifest-reference-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        // 输出摘要
        console.log('\n📋 [ManifestReferenceChecker] 检查结果摘要:');
        console.log(`📁 总文件数: ${report.summary.totalFiles}`);
        console.log(`✅ 存在文件: ${report.summary.existingFiles}`);
        console.log(`❌ 缺失文件: ${report.summary.missingFiles}`);
        console.log(`🔄 重复引用: ${report.summary.duplicateReferences}`);
        console.log(`🔥 错误数量: ${report.summary.errors}`);
        
        if (this.checkResults.missingFiles.length > 0) {
            console.log('\n❌ 缺失的文件:');
            this.checkResults.missingFiles.forEach(file => {
                console.log(`   - ${file.path}`);
            });
        }
        
        if (this.checkResults.duplicateReferences.length > 0) {
            console.log('\n🔄 重复引用的文件:');
            this.checkResults.duplicateReferences.forEach(dup => {
                console.log(`   - ${dup.path} (${dup.count}次)`);
            });
        }
        
        console.log(`\n📄 详细报告已保存到: ${reportPath}`);
        
        return report;
    }
}

// 执行检查
if (require.main === module) {
    const checker = new ManifestReferenceChecker();
    checker.runCheck().then(() => {
        console.log('🎉 [ManifestReferenceChecker] 检查完成');
    }).catch(error => {
        console.error('💥 [ManifestReferenceChecker] 检查失败:', error);
        process.exit(1);
    });
}

module.exports = ManifestReferenceChecker;
