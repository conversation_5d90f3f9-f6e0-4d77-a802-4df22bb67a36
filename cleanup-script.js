/**
 * MDAC Chrome扩展清理脚本
 * 自动化清理过时文件和重复代码
 * 创建日期: 2025-01-11
 */

const fs = require('fs');
const path = require('path');

class MDACCleanupScript {
    constructor() {
        this.projectRoot = process.cwd();
        this.cleanupReport = {
            deletedFiles: [],
            movedFiles: [],
            updatedFiles: [],
            errors: [],
            warnings: []
        };

        console.log('🧹 [MDACCleanupScript] 初始化清理脚本');
        console.log('📁 项目根目录:', this.projectRoot);
    }

    /**
     * 执行完整清理流程
     */
    async executeCleanup() {
        try {
            console.log('🚀 [MDACCleanupScript] 开始执行清理流程');

            // 1. 删除重复的模块文件
            await this.removeDuplicateModules();

            // 2. 清理过时的工具文件
            await this.cleanupObsoleteUtils();

            // 3. 整理配置文件
            await this.organizeConfigFiles();

            // 4. 清理未使用的CSS
            await this.cleanupUnusedCSS();

            // 5. 验证文件引用
            await this.validateFileReferences();

            // 6. 生成清理报告
            this.generateCleanupReport();

            console.log('✅ [MDACCleanupScript] 清理流程完成');

        } catch (error) {
            console.error('❌ [MDACCleanupScript] 清理流程失败', error);
            this.cleanupReport.errors.push({
                type: 'cleanup_failed',
                message: error.message,
                stack: error.stack
            });
        }
    }

    /**
     * 删除重复的模块文件
     */
    async removeDuplicateModules() {
        console.log('🗑️ [MDACCleanupScript] 删除重复的模块文件');

        const duplicateFiles = [
            // 已经在新模块化架构中重新实现的文件
            'modules/confidence-evaluator.js',
            'modules/progress-visualizer.js', 
            'modules/logger.js',
            'modules/debug-console.js',
            'modules/data-preview-manager.js',
            'modules/date-formatter.js',
            'modules/form-validator.js',
            
            // 功能已整合到新模块中的文件
            'utils/enhanced-form-filler.js', // 功能已整合到FormFiller.js
            'utils/mdac-validator.js', // 功能已整合到DataValidator.js
        ];

        for (const filePath of duplicateFiles) {
            await this.safeDeleteFile(filePath);
        }
    }

    /**
     * 清理过时的工具文件
     */
    async cleanupObsoleteUtils() {
        console.log('🧹 [MDACCleanupScript] 清理过时的工具文件');

        // 检查utils目录下的文件
        const utilsDir = path.join(this.projectRoot, 'utils');
        
        if (fs.existsSync(utilsDir)) {
            const files = fs.readdirSync(utilsDir);
            
            for (const file of files) {
                const filePath = path.join(utilsDir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.isFile() && file.endsWith('.js')) {
                    // 检查文件是否被引用
                    const isReferenced = await this.checkFileReferences(path.join('utils', file));
                    
                    if (!isReferenced) {
                        this.cleanupReport.warnings.push({
                            type: 'unreferenced_file',
                            file: path.join('utils', file),
                            message: '文件未被引用，可能可以删除'
                        });
                    }
                }
            }
        }
    }

    /**
     * 整理配置文件
     */
    async organizeConfigFiles() {
        console.log('📋 [MDACCleanupScript] 整理配置文件');

        const configMoves = [
            // 如果有配置文件需要移动到统一位置
            {
                from: 'config/ai-config.js',
                to: 'ui/sidepanel/config/ai-config.js'
            }
        ];

        for (const move of configMoves) {
            await this.safeMoveFile(move.from, move.to);
        }
    }

    /**
     * 清理未使用的CSS
     */
    async cleanupUnusedCSS() {
        console.log('🎨 [MDACCleanupScript] 清理未使用的CSS');

        // 检查CSS文件中是否有未使用的样式
        const cssFiles = [
            'ui/ui-sidepanel.css',
            'content/content-styles.css'
        ];

        for (const cssFile of cssFiles) {
            const fullPath = path.join(this.projectRoot, cssFile);
            
            if (fs.existsSync(fullPath)) {
                const content = fs.readFileSync(fullPath, 'utf8');
                
                // 检查是否有明显的过时样式
                const obsoletePatterns = [
                    /\.old-/g,
                    /\.deprecated-/g,
                    /\/\* TODO: remove \*\//g,
                    /\/\* DEPRECATED \*\//g
                ];

                let hasObsoleteStyles = false;
                for (const pattern of obsoletePatterns) {
                    if (pattern.test(content)) {
                        hasObsoleteStyles = true;
                        break;
                    }
                }

                if (hasObsoleteStyles) {
                    this.cleanupReport.warnings.push({
                        type: 'obsolete_css',
                        file: cssFile,
                        message: '包含可能过时的CSS样式'
                    });
                }
            }
        }
    }

    /**
     * 验证文件引用
     */
    async validateFileReferences() {
        console.log('🔍 [MDACCleanupScript] 验证文件引用');

        // 检查HTML文件中的脚本引用
        const htmlFiles = [
            'ui/ui-sidepanel.html'
        ];

        for (const htmlFile of htmlFiles) {
            await this.validateHTMLReferences(htmlFile);
        }

        // 检查manifest.json中的文件引用
        await this.validateManifestReferences();
    }

    /**
     * 验证HTML文件中的引用
     */
    async validateHTMLReferences(htmlFile) {
        const fullPath = path.join(this.projectRoot, htmlFile);
        
        if (!fs.existsSync(fullPath)) {
            this.cleanupReport.errors.push({
                type: 'missing_html_file',
                file: htmlFile,
                message: 'HTML文件不存在'
            });
            return;
        }

        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 检查脚本引用
        const scriptMatches = content.match(/<script[^>]+src=["']([^"']+)["']/g);
        
        if (scriptMatches) {
            for (const match of scriptMatches) {
                const srcMatch = match.match(/src=["']([^"']+)["']/);
                if (srcMatch) {
                    const scriptPath = srcMatch[1];
                    const fullScriptPath = path.join(this.projectRoot, 'ui', scriptPath);
                    
                    if (!fs.existsSync(fullScriptPath)) {
                        this.cleanupReport.errors.push({
                            type: 'missing_script_reference',
                            file: htmlFile,
                            referencedFile: scriptPath,
                            message: '引用的脚本文件不存在'
                        });
                    }
                }
            }
        }

        // 检查CSS引用
        const cssMatches = content.match(/<link[^>]+href=["']([^"']+\.css)["']/g);
        
        if (cssMatches) {
            for (const match of cssMatches) {
                const hrefMatch = match.match(/href=["']([^"']+)["']/);
                if (hrefMatch) {
                    const cssPath = hrefMatch[1];
                    const fullCSSPath = path.join(this.projectRoot, 'ui', cssPath);
                    
                    if (!fs.existsSync(fullCSSPath)) {
                        this.cleanupReport.errors.push({
                            type: 'missing_css_reference',
                            file: htmlFile,
                            referencedFile: cssPath,
                            message: '引用的CSS文件不存在'
                        });
                    }
                }
            }
        }
    }

    /**
     * 验证manifest.json中的引用
     */
    async validateManifestReferences() {
        const manifestPath = path.join(this.projectRoot, 'manifest.json');
        
        if (!fs.existsSync(manifestPath)) {
            this.cleanupReport.errors.push({
                type: 'missing_manifest',
                message: 'manifest.json文件不存在'
            });
            return;
        }

        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        
        // 检查web_accessible_resources中的文件
        if (manifest.web_accessible_resources) {
            for (const resource of manifest.web_accessible_resources) {
                if (resource.resources) {
                    for (const file of resource.resources) {
                        const fullPath = path.join(this.projectRoot, file);
                        
                        if (!fs.existsSync(fullPath)) {
                            this.cleanupReport.errors.push({
                                type: 'missing_web_accessible_resource',
                                file: file,
                                message: 'web_accessible_resources中引用的文件不存在'
                            });
                        }
                    }
                }
            }
        }

        // 检查content_scripts中的文件
        if (manifest.content_scripts) {
            for (const script of manifest.content_scripts) {
                if (script.js) {
                    for (const file of script.js) {
                        const fullPath = path.join(this.projectRoot, file);
                        
                        if (!fs.existsSync(fullPath)) {
                            this.cleanupReport.errors.push({
                                type: 'missing_content_script',
                                file: file,
                                message: 'content_scripts中引用的JS文件不存在'
                            });
                        }
                    }
                }
                
                if (script.css) {
                    for (const file of script.css) {
                        const fullPath = path.join(this.projectRoot, file);
                        
                        if (!fs.existsSync(fullPath)) {
                            this.cleanupReport.errors.push({
                                type: 'missing_content_css',
                                file: file,
                                message: 'content_scripts中引用的CSS文件不存在'
                            });
                        }
                    }
                }
            }
        }
    }

    /**
     * 安全删除文件
     */
    async safeDeleteFile(filePath) {
        const fullPath = path.join(this.projectRoot, filePath);
        
        if (fs.existsSync(fullPath)) {
            try {
                // 创建备份
                const backupPath = fullPath + '.backup.' + Date.now();
                fs.copyFileSync(fullPath, backupPath);
                
                // 删除原文件
                fs.unlinkSync(fullPath);
                
                this.cleanupReport.deletedFiles.push({
                    original: filePath,
                    backup: backupPath,
                    timestamp: Date.now()
                });
                
                console.log(`🗑️ 已删除文件: ${filePath} (备份: ${backupPath})`);
                
            } catch (error) {
                this.cleanupReport.errors.push({
                    type: 'delete_failed',
                    file: filePath,
                    message: error.message
                });
                
                console.error(`❌ 删除文件失败: ${filePath}`, error);
            }
        } else {
            console.log(`⚠️ 文件不存在，跳过删除: ${filePath}`);
        }
    }

    /**
     * 安全移动文件
     */
    async safeMoveFile(fromPath, toPath) {
        const fullFromPath = path.join(this.projectRoot, fromPath);
        const fullToPath = path.join(this.projectRoot, toPath);
        
        if (fs.existsSync(fullFromPath)) {
            try {
                // 确保目标目录存在
                const toDir = path.dirname(fullToPath);
                if (!fs.existsSync(toDir)) {
                    fs.mkdirSync(toDir, { recursive: true });
                }
                
                // 移动文件
                fs.renameSync(fullFromPath, fullToPath);
                
                this.cleanupReport.movedFiles.push({
                    from: fromPath,
                    to: toPath,
                    timestamp: Date.now()
                });
                
                console.log(`📁 已移动文件: ${fromPath} -> ${toPath}`);
                
            } catch (error) {
                this.cleanupReport.errors.push({
                    type: 'move_failed',
                    from: fromPath,
                    to: toPath,
                    message: error.message
                });
                
                console.error(`❌ 移动文件失败: ${fromPath} -> ${toPath}`, error);
            }
        } else {
            console.log(`⚠️ 源文件不存在，跳过移动: ${fromPath}`);
        }
    }

    /**
     * 检查文件是否被引用
     */
    async checkFileReferences(filePath) {
        // 简化的引用检查 - 在实际项目中可以更复杂
        const searchPaths = [
            'ui/ui-sidepanel.html',
            'manifest.json',
            'ui/sidepanel/ui-sidepanel-modular.js'
        ];

        for (const searchPath of searchPaths) {
            const fullSearchPath = path.join(this.projectRoot, searchPath);
            
            if (fs.existsSync(fullSearchPath)) {
                const content = fs.readFileSync(fullSearchPath, 'utf8');
                
                if (content.includes(filePath) || content.includes(path.basename(filePath))) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 生成清理报告
     */
    generateCleanupReport() {
        const reportPath = path.join(this.projectRoot, 'cleanup-report.json');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                deletedFiles: this.cleanupReport.deletedFiles.length,
                movedFiles: this.cleanupReport.movedFiles.length,
                updatedFiles: this.cleanupReport.updatedFiles.length,
                errors: this.cleanupReport.errors.length,
                warnings: this.cleanupReport.warnings.length
            },
            details: this.cleanupReport
        };

        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('📊 [MDACCleanupScript] 清理报告已生成:', reportPath);
        console.log('📈 清理统计:', report.summary);

        // 如果有错误，显示错误信息
        if (report.summary.errors > 0) {
            console.log('❌ 发现错误:');
            this.cleanupReport.errors.forEach(error => {
                console.log(`  - ${error.type}: ${error.message}`);
            });
        }

        // 如果有警告，显示警告信息
        if (report.summary.warnings > 0) {
            console.log('⚠️ 发现警告:');
            this.cleanupReport.warnings.forEach(warning => {
                console.log(`  - ${warning.type}: ${warning.message}`);
            });
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const cleanup = new MDACCleanupScript();
    cleanup.executeCleanup().then(() => {
        console.log('🎉 清理脚本执行完成');
        process.exit(0);
    }).catch(error => {
        console.error('💥 清理脚本执行失败', error);
        process.exit(1);
    });
}

module.exports = MDACCleanupScript;
