/**
 * MDAC增强表单填充管理器
 * 基于严格的MDAC规格要求进行智能表单填充
 * 更新日期: 2025-01-11 - 添加字段可用性检查和缺失字段处理
 */

class EnhancedFormFiller {
    constructor() {
        this.validator = new MDACValidator();
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        this.fillTimeout = 30000;
        this.fieldMappings = this.initializeFieldMappings();

        // 字段可用性配置引用
        this.fieldConfig = typeof MDAC_FIELD_CONFIG !== 'undefined' ? MDAC_FIELD_CONFIG : null;

        // 填充统计
        this.fillStats = {
            total: 0,
            successful: 0,
            failed: 0,
            skipped: 0,
            unavailable: 0
        };

        console.log('🔧 EnhancedFormFiller初始化完成，字段配置:', this.fieldConfig ? '✅ 已加载' : '❌ 未找到');
    }

    /**
     * 初始化字段映射关系
     */
    initializeFieldMappings() {
        return {
            // 个人信息字段映射
            personal: {
                'name': {
                    selectors: ['input[name="name"]', '#name', 'input[placeholder*="name"]'],
                    type: 'text',
                    validator: 'validateName',
                    formatter: 'formatName'
                },
                'passportNo': {
                    selectors: ['input[name="passportNo"]', '#passportNo', 'input[placeholder*="passport"]'],
                    type: 'text',
                    validator: 'validatePassportNo',
                    formatter: 'formatPassportNo'
                },
                'dateOfBirth': {
                    selectors: ['input[name="dateOfBirth"]', '#dateOfBirth', 'input[type="date"][name*="birth"]'],
                    type: 'date',
                    validator: 'validateDate',
                    formatter: 'formatDate'
                },
                'nationality': {
                    selectors: ['select[name="nationality"]', '#nationality'],
                    type: 'select',
                    validator: 'validateNationality',
                    formatter: 'formatNationality'
                },
                'sex': {
                    selectors: ['select[name="sex"]', '#sex', 'select[name="gender"]'],
                    type: 'select',
                    validator: 'validateSex',
                    formatter: 'formatSex'
                },
                'passportExpiry': {
                    selectors: ['input[name="passportExpiry"]', '#passportExpiry'],
                    type: 'date',
                    validator: 'validateDate',
                    formatter: 'formatDate'
                },
                'email': {
                    selectors: ['input[name="email"]', '#email', 'input[type="email"]'],
                    type: 'email',
                    validator: 'validateEmail',
                    formatter: 'formatEmail'
                },
                'confirmEmail': {
                    selectors: ['input[name="confirmEmail"]', '#confirmEmail'],
                    type: 'email',
                    validator: 'validateEmail',
                    formatter: 'formatEmail'
                },
                'countryCode': {
                    selectors: ['select[name="countryCode"]', '#countryCode'],
                    type: 'select',
                    validator: 'validateCountryCode',
                    formatter: 'formatCountryCode'
                },
                'mobileNo': {
                    selectors: ['input[name="mobileNo"]', '#mobileNo', 'input[name="mobile"]'],
                    type: 'text',
                    validator: 'validateMobileNo',
                    formatter: 'formatMobileNo'
                }
            },
            // 旅行信息字段映射
            travel: {
                'arrivalDate': {
                    selectors: ['input[name="arrivalDate"]', '#arrivalDate'],
                    type: 'date',
                    validator: 'validateDate',
                    formatter: 'formatDate'
                },
                'departureDate': {
                    selectors: ['input[name="departureDate"]', '#departureDate'],
                    type: 'date',
                    validator: 'validateDate',
                    formatter: 'formatDate'
                },
                'flightNo': {
                    selectors: ['input[name="flightNo"]', '#flightNo'],
                    type: 'text',
                    validator: 'validateFlightNo',
                    formatter: 'formatFlightNo'
                },
                'modeOfTravel': {
                    selectors: ['select[name="modeOfTravel"]', '#modeOfTravel'],
                    type: 'select',
                    validator: 'validateModeOfTravel',
                    formatter: 'formatModeOfTravel'
                },
                'lastPort': {
                    selectors: ['select[name="lastPort"]', '#lastPort'],
                    type: 'select',
                    validator: 'validateLastPort',
                    formatter: 'formatLastPort'
                },
                'accommodation': {
                    selectors: ['select[name="accommodation"]', '#accommodation'],
                    type: 'select',
                    validator: 'validateAccommodation',
                    formatter: 'formatAccommodation'
                },
                'address': {
                    selectors: ['input[name="address"]', '#address', 'textarea[name="address"]'],
                    type: 'text',
                    validator: 'validateAddress',
                    formatter: 'formatAddress'
                },
                'address2': {
                    selectors: ['input[name="address2"]', '#address2'],
                    type: 'text',
                    validator: 'validateAddress',
                    formatter: 'formatAddress'
                },
                'state': {
                    selectors: ['select[name="state"]', '#state'],
                    type: 'select',
                    validator: 'validateState',
                    formatter: 'formatState'
                },
                'postcode': {
                    selectors: ['input[name="postcode"]', '#postcode'],
                    type: 'text',
                    validator: 'validatePostcode',
                    formatter: 'formatPostcode'
                },
                'city': {
                    selectors: ['select[name="city"]', '#city'],
                    type: 'select',
                    validator: 'validateCity',
                    formatter: 'formatCity'
                }
            }
        };
    }

    /**
     * 主要填充方法 - 智能填充表单
     */
    async fillForm(formData, options = {}) {
        // 记录开始日志
        if (window.mdacLogger) {
            window.mdacLogger.info('FORM', '开始增强表单填充');
            window.mdacLogger.startPerformance('enhancedFormFill');
            window.mdacLogger.debug('FORM', '表单数据概览', {
                fieldsCount: Object.keys(formData).length,
                fields: Object.keys(formData),
                options: options
            });
        }

        const result = {
            success: false,
            filledFields: [],
            failedFields: [],
            errors: [],
            warnings: []
        };

        try {
            console.log('🚀 开始智能表单填充...');

            // 1. 预验证数据
            const validationResult = this.validator.validateFormData(formData);
            if (!validationResult.isValid) {
                result.errors.push('数据验证失败: ' + validationResult.errors.join(', '));
                return result;
            }

            // 2. 检测MDAC页面
            if (!this.isMDACPage()) {
                result.errors.push('当前页面不是MDAC网站');
                return result;
            }

            // 3. 等待页面加载完成
            await this.waitForPageReady();

            // 4. 分组填充字段
            await this.fillPersonalInfo(formData, result);
            await this.fillTravelInfo(formData, result);

            // 5. 验证填充结果
            await this.validateFilledForm(result);

            // 6. 生成填充统计报告
            const statsReport = this.getFillStatsReport();
            result.fillStats = statsReport;

            // 7. 设置成功状态
            result.success = result.failedFields.length === 0;

            console.log('📊 填充统计报告:', statsReport.summary);
            console.log('✅ 表单填充完成', result);
            return result;

        } catch (error) {
            console.error('❌ 表单填充失败:', error);
            result.errors.push('填充过程中发生错误: ' + error.message);
            return result;
        }
    }

    /**
     * 检查字段可用性
     * @param {string} category - 字段类别 (personal, travel, accommodation)
     * @param {string} fieldName - 字段名称
     * @returns {object} 可用性信息
     */
    checkFieldAvailability(category, fieldName) {
        if (!this.fieldConfig) {
            // 如果没有配置，默认认为字段可用
            return { available: true, reason: '默认可用' };
        }

        const fieldInfo = this.fieldConfig.getFieldAvailability(category, fieldName);
        if (!fieldInfo) {
            // 如果配置中没有该字段，默认认为可用
            return { available: true, reason: '配置中未定义，默认可用' };
        }

        return fieldInfo;
    }

    /**
     * 记录字段填充状态
     * @param {string} fieldName - 字段名称
     * @param {string} status - 状态 (success, failed, skipped, unavailable)
     * @param {string} reason - 原因
     */
    recordFieldStatus(fieldName, status, reason = '') {
        this.fillStats.total++;

        switch (status) {
            case 'success':
                this.fillStats.successful++;
                console.log(`✅ ${fieldName}: ${this.fieldConfig?.FIELD_STATUS_MESSAGES?.success || '已填充'}`);
                break;
            case 'failed':
                this.fillStats.failed++;
                console.log(`❌ ${fieldName}: ${this.fieldConfig?.FIELD_STATUS_MESSAGES?.error || '填充失败'} - ${reason}`);
                break;
            case 'skipped':
                this.fillStats.skipped++;
                console.log(`⏭️ ${fieldName}: ${this.fieldConfig?.FIELD_STATUS_MESSAGES?.skipped || '已跳过'} - ${reason}`);
                break;
            case 'unavailable':
                this.fillStats.unavailable++;
                console.log(`⚠️ ${fieldName}: ${this.fieldConfig?.FIELD_STATUS_MESSAGES?.unavailable || 'MDAC网站不需要此信息'} - ${reason}`);
                break;
        }
    }

    /**
     * 获取填充统计报告
     * @returns {object} 统计报告
     */
    getFillStatsReport() {
        const total = this.fillStats.total;
        const successRate = total > 0 ? ((this.fillStats.successful / total) * 100).toFixed(1) : 0;

        return {
            ...this.fillStats,
            successRate: `${successRate}%`,
            summary: `总计 ${total} 个字段：成功 ${this.fillStats.successful}，失败 ${this.fillStats.failed}，跳过 ${this.fillStats.skipped}，不可用 ${this.fillStats.unavailable}`
        };
    }

    /**
     * 填充个人信息字段
     */
    async fillPersonalInfo(formData, result) {
        console.log('📝 填充个人信息字段...');

        const personalFields = this.fieldMappings.personal;

        for (const [fieldName, fieldConfig] of Object.entries(personalFields)) {
            // 检查字段可用性
            const availability = this.checkFieldAvailability('personal', fieldName);

            if (!availability.available) {
                // 字段不可用，记录状态并跳过
                this.recordFieldStatus(fieldName, 'unavailable', availability.reason);
                continue;
            }

            if (formData[fieldName]) {
                await this.fillSingleField(fieldName, formData[fieldName], fieldConfig, result);
            } else {
                // 数据中没有该字段的值
                this.recordFieldStatus(fieldName, 'skipped', '数据中无此字段值');
            }
        }
    }

    /**
     * 填充旅行信息字段
     */
    async fillTravelInfo(formData, result) {
        console.log('✈️ 填充旅行信息字段...');

        const travelFields = this.fieldMappings.travel;

        for (const [fieldName, fieldConfig] of Object.entries(travelFields)) {
            // 检查字段可用性
            const availability = this.checkFieldAvailability('travel', fieldName);

            if (!availability.available) {
                // 字段不可用，记录状态并跳过
                this.recordFieldStatus(fieldName, 'unavailable', availability.reason);

                // 对于日期字段，保留数据供用户参考
                if ((fieldName === 'arrivalDate' || fieldName === 'departureDate') && formData[fieldName]) {
                    result.preservedData = result.preservedData || {};
                    result.preservedData[fieldName] = formData[fieldName];
                    console.log(`📅 ${fieldName} 数据已保留供后续使用: ${formData[fieldName]}`);
                }
                continue;
            }

            if (formData[fieldName]) {
                await this.fillSingleField(fieldName, formData[fieldName], fieldConfig, result);
            } else {
                // 数据中没有该字段的值
                this.recordFieldStatus(fieldName, 'skipped', '数据中无此字段值');
            }
        }
    }

    /**
     * 填充单个字段
     */
    async fillSingleField(fieldName, value, fieldConfig, result) {
        if (window.mdacLogger) {
            window.mdacLogger.debug('FORM', `开始填充字段: ${fieldName}`, {
                value: value,
                selectors: fieldConfig.selectors
            });
        }

        let attempts = 0;

        while (attempts < this.retryAttempts) {
            attempts++;
            try {
                if (window.mdacLogger) {
                    window.mdacLogger.debug('FORM', `字段 ${fieldName} 填充尝试 ${attempts}/${this.retryAttempts}`);
                }

                // 1. 查找字段元素
                const element = this.findFieldElement(fieldConfig.selectors);
                if (!element) {
                    if (window.mdacLogger) {
                        window.mdacLogger.warn('FORM', `字段 ${fieldName} 元素未找到`, {
                            selectors: fieldConfig.selectors
                        });
                    }
                    throw new Error(`字段 ${fieldName} 未找到`);
                }

                if (window.mdacLogger) {
                    window.mdacLogger.debug('FORM', `字段 ${fieldName} 元素找到`, {
                        tagName: element.tagName,
                        id: element.id,
                        name: element.name,
                        type: element.type
                    });
                }

                // 2. 格式化数据
                const formattedValue = this.formatFieldValue(value, fieldConfig.formatter);

                // 3. 验证数据
                if (!this.validateFieldValue(formattedValue, fieldConfig.validator)) {
                    throw new Error(`字段 ${fieldName} 数据验证失败`);
                }

                // 4. 填充字段
                await this.setFieldValue(element, formattedValue, fieldConfig.type);

                // 5. 验证填充结果
                await this.verifyFieldFilled(element, formattedValue);

                result.filledFields.push({
                    field: fieldName,
                    value: formattedValue,
                    element: element.tagName + (element.id ? '#' + element.id : '')
                });

                // 记录成功状态
                this.recordFieldStatus(fieldName, 'success');
                return;

            } catch (error) {
                attempts++;
                console.warn(`⚠️ 字段 ${fieldName} 填充失败 (尝试 ${attempts}/${this.retryAttempts}): ${error.message}`);

                if (attempts < this.retryAttempts) {
                    await this.delay(this.retryDelay);
                } else {
                    result.failedFields.push({
                        field: fieldName,
                        value: value,
                        error: error.message
                    });

                    // 记录失败状态
                    this.recordFieldStatus(fieldName, 'failed', error.message);
                }
            }
        }
    }

    /**
     * 查找字段元素
     */
    findFieldElement(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && this.isElementVisible(element)) {
                return element;
            }
        }
        return null;
    }

    /**
     * 设置字段值
     */
    async setFieldValue(element, value, type) {
        // 聚焦元素
        element.focus();
        await this.delay(100);

        switch (type) {
            case 'text':
            case 'email':
            case 'date':
                // 清空现有值
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
                
                // 设置新值
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                break;

            case 'select':
                // 选择选项
                const option = Array.from(element.options).find(opt => 
                    opt.value === value || opt.text === value
                );
                if (option) {
                    element.selectedIndex = option.index;
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    throw new Error(`选项 ${value} 未找到`);
                }
                break;

            default:
                element.value = value;
                element.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // 失焦
        element.blur();
        await this.delay(100);
    }

    /**
     * 验证字段是否填充成功
     */
    async verifyFieldFilled(element, expectedValue) {
        await this.delay(200); // 等待值更新
        
        const actualValue = element.type === 'select-one' ? 
            element.options[element.selectedIndex]?.value : 
            element.value;

        if (actualValue !== expectedValue) {
            throw new Error(`填充验证失败: 期望 ${expectedValue}, 实际 ${actualValue}`);
        }
    }

    /**
     * 格式化字段值
     */
    formatFieldValue(value, formatter) {
        if (!formatter || !this[formatter]) {
            return value;
        }
        return this[formatter](value);
    }

    /**
     * 验证字段值
     */
    validateFieldValue(value, validator) {
        if (!validator || !this.validator[validator]) {
            return true;
        }
        return this.validator[validator](value);
    }

    /**
     * 检查是否为MDAC页面
     */
    isMDACPage() {
        return window.location.hostname.includes('mdac') || 
               document.title.includes('MDAC') ||
               document.querySelector('form[action*="mdac"]') !== null;
    }

    /**
     * 等待页面准备就绪
     */
    async waitForPageReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve, { once: true });
            }
        });
    }

    /**
     * 检查元素是否可见
     */
    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               element.offsetParent !== null;
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 验证填充后的表单
     */
    async validateFilledForm(result) {
        // 检查必填字段是否都已填充
        const requiredFields = [
            'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
            'email', 'arrivalDate', 'departureDate', 'address'
        ];

        const missingFields = requiredFields.filter(field => 
            !result.filledFields.some(filled => filled.field === field)
        );

        if (missingFields.length > 0) {
            result.warnings.push(`缺少必填字段: ${missingFields.join(', ')}`);
        }
    }

    // 格式化方法
    formatName(value) { return value.toUpperCase(); }
    formatPassportNo(value) { return value.toUpperCase(); }
    formatDate(value) { return this.validator.formatDate(value); }
    formatEmail(value) { return value.toLowerCase(); }
    formatMobileNo(value) { return value.replace(/\D/g, ''); }
    formatPostcode(value) { return value.replace(/\D/g, ''); }
    formatAddress(value) { return value.trim(); }
    formatFlightNo(value) { return value.toUpperCase(); }

    // 其他格式化方法使用智能代码映射
    formatNationality(value) { return this.validator.smartCodeMapping(value, 'nationality') || value; }
    formatSex(value) { return value === '男' || value === 'Male' ? '1' : value === '女' || value === 'Female' ? '2' : value; }
    formatCountryCode(value) { return value.startsWith('+') ? value : '+' + value; }
    formatModeOfTravel(value) { return value.toUpperCase(); }
    formatAccommodation(value) { return this.validator.smartCodeMapping(value, 'accommodation') || value; }
    formatState(value) { return this.validator.smartStateMapping(value) || value; }
    formatCity(value) { return this.validator.smartCityMapping(value) || value; }
    formatLastPort(value) { return value.toUpperCase(); }
    
    /**
     * 智能城市代码匹配（使用完整数据库）
     */
    smartMatchCityCode(value, stateCode = null) {
        console.log(`🏙️ 智能匹配城市代码: ${value}, 州属: ${stateCode}`);
        
        // 使用验证器的智能城市匹配功能
        const cityCode = this.validator.smartCityMapping(value, stateCode);
        if (cityCode) {
            console.log(`✅ 成功匹配城市代码: ${value} → ${cityCode}`);
            return cityCode;
        }
        
        // 如果没有找到，尝试邮政编码匹配
        if (this.isPostcode(value)) {
            const postcodeCity = this.validator.getCityByPostcode(value);
            if (postcodeCity) {
                console.log(`✅ 通过邮政编码匹配城市: ${value} → ${postcodeCity}`);
                return postcodeCity;
            }
        }
        
        // 如果指定了州属，返回该州属的默认城市
        if (stateCode) {
            const cities = this.validator.getCitiesByState(stateCode);
            if (cities.length > 0) {
                console.log(`✅ 使用州属默认城市: 州属${stateCode} → ${cities[0].code}`);
                return cities[0].code;
            }
        }
        
        console.warn(`⚠️ 无法匹配城市代码: ${value}`);
        return value;
    }

    /**
     * 智能州属代码匹配（使用完整数据库）
     */
    smartMatchStateCode(value) {
        console.log(`🌍 智能匹配州属代码: ${value}`);
        
        // 使用验证器的智能州属匹配功能
        const stateCode = this.validator.smartStateMapping(value);
        if (stateCode) {
            console.log(`✅ 成功匹配州属代码: ${value} → ${stateCode}`);
            return stateCode;
        }
        
        console.warn(`⚠️ 无法匹配州属代码: ${value}`);
        return value;
    }

    /**
     * 检查是否为邮政编码格式
     */
    isPostcode(value) {
        const postcodePattern = /^\d{5}$/;
        return postcodePattern.test(value.toString().trim());
    }

    /**
     * 获取州属城市列表（用于动态选项）
     */
    getCitiesByState(stateCode) {
        return this.validator.getCitiesByState(stateCode);
    }

    /**
     * 获取热门旅游目的地
     */
    getPopularDestinations() {
        return this.validator.getPopularDestinations();
    }

    /**
     * 验证城市州属匹配
     */
    validateCityStateMatch(cityCode, stateCode) {
        const isValid = this.validator.validateCityStateMatch(cityCode, stateCode);
        if (!isValid) {
            console.warn(`⚠️ 城市与州属不匹配: 城市${cityCode}, 州属${stateCode}`);
        }
        return isValid;
    }
}

// 导出增强表单填充器
if (typeof window !== 'undefined') {
    window.EnhancedFormFiller = EnhancedFormFiller;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedFormFiller;
}
