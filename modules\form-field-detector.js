/**
 * MDAC AI智能分析工具 - 智能表单字段检测系统
 * 动态检测和映射MDAC网站的表单字段，替换硬编码映射
 */

class FormFieldDetector {
    constructor() {
        // 字段检测模式配置
        this.fieldPatterns = {
            // 姓名字段模式
            name: {
                ids: ['name', 'fullname', 'applicant', 'applicantName', 'full_name'],
                names: ['name', 'fullname', 'applicant', 'applicantName'],
                classes: ['name-field', 'applicant-name', 'full-name'],
                placeholders: ['姓名', 'name', 'full name', '英文姓名', 'applicant name'],
                labels: ['姓名', 'name', 'full name', '申请人姓名', 'applicant name'],
                types: ['text'],
                priority: 10
            },
            
            // 护照号码字段模式
            passportNo: {
                ids: ['passNo', 'passport', 'passportNo', 'passport_no', 'pass_no'],
                names: ['passNo', 'passport', 'passportNo', 'passport_no'],
                classes: ['passport-field', 'pass-no', 'passport-number'],
                placeholders: ['护照号码', 'passport', 'passport number', '护照号'],
                labels: ['护照号码', 'passport', 'passport number', '护照号'],
                types: ['text'],
                priority: 10
            },
            
            // 出生日期字段模式
            dateOfBirth: {
                ids: ['dob', 'dateOfBirth', 'birth_date', 'birthDate'],
                names: ['dob', 'dateOfBirth', 'birth_date', 'birthDate'],
                classes: ['dob-field', 'birth-date', 'date-of-birth'],
                placeholders: ['出生日期', 'date of birth', 'dob', 'DD/MM/YYYY'],
                labels: ['出生日期', 'date of birth', 'dob', '生日'],
                types: ['text', 'date'],
                priority: 9
            },
            
            // 国籍字段模式
            nationality: {
                ids: ['nationality', 'country', 'nation', 'countryCode'],
                names: ['nationality', 'country', 'nation', 'countryCode'],
                classes: ['nationality-field', 'country-select', 'nation'],
                placeholders: ['国籍', 'nationality', 'country'],
                labels: ['国籍', 'nationality', 'country', '国家'],
                types: ['select-one', 'text'],
                priority: 8
            },
            
            // 性别字段模式
            sex: {
                ids: ['sex', 'gender', 'sexCode'],
                names: ['sex', 'gender', 'sexCode'],
                classes: ['sex-field', 'gender-select'],
                placeholders: ['性别', 'sex', 'gender'],
                labels: ['性别', 'sex', 'gender'],
                types: ['select-one', 'radio'],
                priority: 7
            },
            
            // 护照到期日期字段模式
            passportExpiry: {
                ids: ['passExpDte', 'passExpiry', 'passportExpiry', 'passport_expiry', 'expiryDate'],  // 添加MDAC实际使用的ID
                names: ['passExpDte', 'passExpiry', 'passportExpiry', 'passport_expiry'],
                classes: ['passport-expiry', 'expiry-date'],
                placeholders: ['护照到期日', 'passport expiry', 'expiry date'],
                labels: ['护照到期日', 'passport expiry', 'expiry date', 'Date of Passport Expiry'],
                types: ['text', 'date'],
                priority: 8
            },
            
            // 电子邮箱字段模式
            email: {
                ids: ['email', 'emailAddress', 'email_address', 'mail'],
                names: ['email', 'emailAddress', 'email_address'],
                classes: ['email-field', 'email-input'],
                placeholders: ['电子邮箱', 'email', 'email address', '邮箱'],
                labels: ['电子邮箱', 'email', 'email address', '邮箱'],
                types: ['email', 'text'],
                priority: 9
            },
            
            // 确认邮箱字段模式
            confirmEmail: {
                ids: ['confirmEmail', 'confirm_email', 'emailConfirm', 'email2'],
                names: ['confirmEmail', 'confirm_email', 'emailConfirm'],
                classes: ['confirm-email', 'email-confirm'],
                placeholders: ['确认邮箱', 'confirm email', 'email confirmation'],
                labels: ['确认邮箱', 'confirm email', 'email confirmation'],
                types: ['email', 'text'],
                priority: 8
            },
            
            // 国家代码字段模式
            countryCode: {
                ids: ['region', 'countryCode', 'country_code', 'phoneCountry', 'dialCode'],  // 添加MDAC实际使用的ID
                names: ['region', 'countryCode', 'country_code', 'phoneCountry'],
                classes: ['country-code', 'phone-country', 'dial-code'],
                placeholders: ['+86', '+60', '+1', 'country code'],
                labels: ['国家代码', 'country code', '区号', 'Country / Region Code'],
                types: ['select-one', 'text'],
                priority: 7
            },
            
            // 手机号码字段模式
            mobileNo: {
                ids: ['mobileNo', 'mobile', 'phone', 'phoneNumber', 'mobile_no'],
                names: ['mobileNo', 'mobile', 'phone', 'phoneNumber'],
                classes: ['mobile-field', 'phone-field', 'mobile-number'],
                placeholders: ['手机号码', 'mobile', 'phone number', '电话'],
                labels: ['手机号码', 'mobile', 'phone number', '电话'],
                types: ['tel', 'text'],
                priority: 9
            },
            
            // 到达日期字段模式
            arrivalDate: {
                ids: ['arrDt', 'arrivalDate', 'arrival_date', 'arriveDate', 'entryDate'],  // 添加MDAC实际使用的ID
                names: ['arrDt', 'arrivalDate', 'arrival_date', 'arriveDate'],
                classes: ['arrival-date', 'entry-date'],
                placeholders: ['到达日期', 'arrival date', 'entry date'],
                labels: ['到达日期', 'arrival date', 'entry date', 'Date of Arrival'],
                types: ['text', 'date'],
                priority: 9
            },
            
            // 离开日期字段模式
            departureDate: {
                ids: ['depDt', 'departureDate', 'departure_date', 'leaveDate', 'exitDate'],  // 添加MDAC实际使用的ID
                names: ['depDt', 'departureDate', 'departure_date', 'leaveDate'],
                classes: ['departure-date', 'exit-date'],
                placeholders: ['离开日期', 'departure date', 'exit date'],
                labels: ['离开日期', 'departure date', 'exit date', 'Date of Departure'],
                types: ['text', 'date'],
                priority: 9
            },
            
            // 航班号字段模式
            flightNo: {
                ids: ['vesselNm', 'flightNo', 'flight_no', 'flightNumber', 'vessel'],
                names: ['vesselNm', 'flightNo', 'flight_no', 'flightNumber'],
                classes: ['flight-field', 'vessel-field', 'flight-number'],
                placeholders: ['航班号', 'flight number', 'vessel', '班次'],
                labels: ['航班号', 'flight number', 'vessel', '交通工具'],
                types: ['text'],
                priority: 8
            },
            
            // 旅行方式字段模式
            modeOfTravel: {
                ids: ['trvlMode', 'travelMode', 'travel_mode', 'modeOfTravel'],
                names: ['trvlMode', 'travelMode', 'travel_mode'],
                classes: ['travel-mode', 'mode-field'],
                placeholders: ['旅行方式', 'travel mode', 'mode of travel'],
                labels: ['旅行方式', 'travel mode', 'mode of travel'],
                types: ['select-one'],
                priority: 7
            },
            
            // 最后港口字段模式
            lastPort: {
                ids: ['embark', 'lastPort', 'last_port', 'embarkation'],
                names: ['embark', 'lastPort', 'last_port'],
                classes: ['embark-field', 'last-port'],
                placeholders: ['最后港口', 'last port', 'embarkation'],
                labels: ['最后港口', 'last port', 'embarkation'],
                types: ['select-one', 'text'],
                priority: 7
            },
            
            // 住宿类型字段模式
            accommodation: {
                ids: ['accommodationStay', 'accommodation', 'stay_type', 'lodging'],
                names: ['accommodationStay', 'accommodation', 'stay_type'],
                classes: ['accommodation-field', 'stay-type'],
                placeholders: ['住宿类型', 'accommodation', 'stay type'],
                labels: ['住宿类型', 'accommodation', 'stay type'],
                types: ['select-one'],
                priority: 7
            },
            
            // 地址行1字段模式
            address: {
                ids: ['accommodationAddress1', 'address1', 'address', 'street'],
                names: ['accommodationAddress1', 'address1', 'address'],
                classes: ['address-field', 'address1', 'street-address'],
                placeholders: ['地址', 'address', 'street address', '住址'],
                labels: ['地址', 'address', 'street address', '住址'],
                types: ['text'],
                priority: 8
            },
            
            // 地址行2字段模式
            address2: {
                ids: ['accommodationAddress2', 'address2', 'address_2'],
                names: ['accommodationAddress2', 'address2', 'address_2'],
                classes: ['address2-field', 'address2'],
                placeholders: ['地址2', 'address 2', 'address line 2'],
                labels: ['地址2', 'address 2', 'address line 2'],
                types: ['text'],
                priority: 6
            },
            
            // 州/省字段模式
            state: {
                ids: ['accommodationState', 'state', 'province', 'region'],
                names: ['accommodationState', 'state', 'province'],
                classes: ['state-field', 'province-field'],
                placeholders: ['州/省', 'state', 'province'],
                labels: ['州/省', 'state', 'province'],
                types: ['select-one', 'text'],
                priority: 8
            },
            
            // 邮政编码字段模式
            postcode: {
                ids: ['accommodationPostcode', 'postcode', 'postal_code', 'zip'],
                names: ['accommodationPostcode', 'postcode', 'postal_code'],
                classes: ['postcode-field', 'postal-code', 'zip-code'],
                placeholders: ['邮政编码', 'postcode', 'postal code', 'zip'],
                labels: ['邮政编码', 'postcode', 'postal code', 'zip'],
                types: ['text'],
                priority: 8
            },
            
            // 城市字段模式
            city: {
                ids: ['accommodationCity', 'city', 'town', 'municipality'],
                names: ['accommodationCity', 'city', 'town'],
                classes: ['city-field', 'town-field'],
                placeholders: ['城市', 'city', 'town'],
                labels: ['城市', 'city', 'town'],
                types: ['select-one', 'text'],
                priority: 8
            }
        };
        
        // 检测结果缓存
        this.detectionCache = new Map();
        
        // 检测统计
        this.detectionStats = {
            totalDetections: 0,
            successfulDetections: 0,
            failedDetections: 0,
            cacheHits: 0
        };
    }
    
    /**
     * 检测页面中的所有表单字段
     * @returns {Object} 检测到的字段映射
     */
    async detectFormFields() {
        console.log('🔍 开始智能表单字段检测...');
        
        // 检查缓存
        const pageSignature = this.generatePageSignature();
        if (this.detectionCache.has(pageSignature)) {
            this.detectionStats.cacheHits++;
            console.log('📋 使用缓存的字段检测结果');
            return this.detectionCache.get(pageSignature);
        }
        
        const detectedFields = {};
        const allFormElements = this.getAllFormElements();
        
        console.log(`📝 发现 ${allFormElements.length} 个表单元素`);
        
        // 为每个字段类型进行检测
        for (const [fieldType, patterns] of Object.entries(this.fieldPatterns)) {
            const detectedElement = this.detectFieldByPatterns(fieldType, patterns, allFormElements);
            if (detectedElement) {
                detectedFields[fieldType] = detectedElement;
                this.detectionStats.successfulDetections++;
                console.log(`✅ 检测到字段: ${fieldType} -> ${detectedElement.id || detectedElement.name}`);
            } else {
                this.detectionStats.failedDetections++;
                console.log(`❌ 未检测到字段: ${fieldType}`);
            }
        }
        
        this.detectionStats.totalDetections++;
        
        // 缓存结果
        this.detectionCache.set(pageSignature, detectedFields);
        
        console.log(`🎯 字段检测完成: ${this.detectionStats.successfulDetections}/${Object.keys(this.fieldPatterns).length} 个字段`);
        
        return detectedFields;
    }
    
    /**
     * 获取页面中所有表单元素
     * @returns {Array} 表单元素数组
     */
    getAllFormElements() {
        const selectors = [
            'input[type="text"]',
            'input[type="email"]', 
            'input[type="tel"]',
            'input[type="date"]',
            'input[type="password"]',
            'input[type="radio"]',
            'input[type="checkbox"]',
            'select',
            'textarea'
        ];
        
        const elements = [];
        selectors.forEach(selector => {
            elements.push(...document.querySelectorAll(selector));
        });
        
        return elements;
    }
    
    /**
     * 根据模式检测特定字段
     * @param {string} fieldType 字段类型
     * @param {Object} patterns 匹配模式
     * @param {Array} elements 待检测的元素数组
     * @returns {Element|null} 检测到的元素
     */
    detectFieldByPatterns(fieldType, patterns, elements) {
        let bestMatch = null;
        let bestScore = 0;
        
        for (const element of elements) {
            const score = this.calculateMatchScore(element, patterns);
            if (score > bestScore && score > 0) {
                bestScore = score;
                bestMatch = element;
            }
        }
        
        // 降低阈值以提高检测率，但仍保持质量
        return bestScore >= 2 ? bestMatch : null;
    }
    
    /**
     * 计算元素与模式的匹配分数
     * @param {Element} element 待检测元素
     * @param {Object} patterns 匹配模式
     * @returns {number} 匹配分数
     */
    calculateMatchScore(element, patterns) {
        let score = 0;
        let matchDetails = [];

        // 精确ID匹配 (权重: 10) - 最高优先级
        if (element.id && patterns.ids) {
            for (const pattern of patterns.ids) {
                if (element.id === pattern) {
                    score += 10;
                    matchDetails.push(`精确ID匹配: ${pattern}`);
                    break;
                } else if (element.id.toLowerCase() === pattern.toLowerCase()) {
                    score += 8;
                    matchDetails.push(`ID匹配(忽略大小写): ${pattern}`);
                    break;
                } else if (element.id.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 5;
                    matchDetails.push(`ID包含匹配: ${pattern}`);
                    break;
                }
            }
        }

        // 精确Name属性匹配 (权重: 8)
        if (element.name && patterns.names) {
            for (const pattern of patterns.names) {
                if (element.name === pattern) {
                    score += 8;
                    matchDetails.push(`精确Name匹配: ${pattern}`);
                    break;
                } else if (element.name.toLowerCase() === pattern.toLowerCase()) {
                    score += 6;
                    matchDetails.push(`Name匹配(忽略大小写): ${pattern}`);
                    break;
                } else if (element.name.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 4;
                    matchDetails.push(`Name包含匹配: ${pattern}`);
                    break;
                }
            }
        }

        // Class匹配 (权重: 3)
        if (element.className && patterns.classes) {
            for (const pattern of patterns.classes) {
                if (element.className.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 3;
                    matchDetails.push(`Class匹配: ${pattern}`);
                    break;
                }
            }
        }

        // Placeholder匹配 (权重: 2)
        if (element.placeholder && patterns.placeholders) {
            for (const pattern of patterns.placeholders) {
                if (element.placeholder.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 2;
                    matchDetails.push(`Placeholder匹配: ${pattern}`);
                    break;
                }
            }
        }

        // Label匹配 (权重: 3) - 提高权重，因为label很重要
        const label = this.findAssociatedLabel(element);
        if (label && patterns.labels) {
            for (const pattern of patterns.labels) {
                if (label.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 3;
                    matchDetails.push(`Label匹配: ${pattern}`);
                    break;
                }
            }
        }

        // 类型匹配 (权重: 1)
        if (patterns.types && patterns.types.includes(element.type)) {
            score += 1;
            matchDetails.push(`类型匹配: ${element.type}`);
        }

        // 模糊匹配增强 - 检查相似性
        if (score === 0) {
            score += this.calculateFuzzyMatchScore(element, patterns);
        }

        // 记录匹配详情用于调试
        if (score > 0 && window.mdacLogger) {
            window.mdacLogger.debug('FORM', `字段匹配评分: ${element.id || element.name || 'unnamed'}`, {
                score: score,
                details: matchDetails,
                elementInfo: {
                    id: element.id,
                    name: element.name,
                    type: element.type,
                    placeholder: element.placeholder
                }
            });
        }

        return score;
    }
    
    /**
     * 查找元素关联的标签文本
     * @param {Element} element 表单元素
     * @returns {string|null} 标签文本
     */
    findAssociatedLabel(element) {
        // 通过for属性查找
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) return label.textContent.trim();
        }
        
        // 查找父级label
        let parent = element.parentElement;
        while (parent && parent.tagName !== 'FORM') {
            if (parent.tagName === 'LABEL') {
                return parent.textContent.trim();
            }
            parent = parent.parentElement;
        }
        
        // 查找前面的文本节点或标签
        const prevElement = element.previousElementSibling;
        if (prevElement && (prevElement.tagName === 'LABEL' || prevElement.tagName === 'SPAN')) {
            return prevElement.textContent.trim();
        }
        
        return null;
    }
    
    /**
     * 生成页面签名用于缓存
     * @returns {string} 页面签名
     */
    generatePageSignature() {
        const forms = document.querySelectorAll('form');
        let signature = window.location.pathname;
        
        forms.forEach(form => {
            signature += form.innerHTML.length;
        });
        
        return btoa(signature).substring(0, 16);
    }
    
    /**
     * 获取检测统计信息
     * @returns {Object} 统计信息
     */
    getDetectionStats() {
        return {
            ...this.detectionStats,
            successRate: this.detectionStats.totalDetections > 0 
                ? (this.detectionStats.successfulDetections / (this.detectionStats.successfulDetections + this.detectionStats.failedDetections) * 100).toFixed(2)
                : 0,
            cacheHitRate: this.detectionStats.totalDetections > 0
                ? (this.detectionStats.cacheHits / this.detectionStats.totalDetections * 100).toFixed(2)
                : 0
        };
    }
    
    /**
     * 清除检测缓存
     */
    clearCache() {
        this.detectionCache.clear();
        console.log('🗑️ 字段检测缓存已清除');
    }
    
    /**
     * 验证检测结果的准确性
     * @param {Object} detectedFields 检测到的字段
     * @returns {Object} 验证结果
     */
    validateDetection(detectedFields) {
        const validation = {
            isValid: true,
            missingCriticalFields: [],
            duplicateFields: [],
            confidence: 0
        };
        
        // 检查关键字段
        const criticalFields = ['name', 'passportNo', 'email', 'mobileNo'];
        for (const field of criticalFields) {
            if (!detectedFields[field]) {
                validation.missingCriticalFields.push(field);
                validation.isValid = false;
            }
        }
        
        // 检查重复字段
        const elementIds = new Set();
        for (const [fieldType, element] of Object.entries(detectedFields)) {
            const elementId = element.id || element.name || element.className;
            if (elementIds.has(elementId)) {
                validation.duplicateFields.push(fieldType);
                validation.isValid = false;
            }
            elementIds.add(elementId);
        }
        
        // 计算置信度
        const detectedCount = Object.keys(detectedFields).length;
        const totalFields = Object.keys(this.fieldPatterns).length;
        validation.confidence = (detectedCount / totalFields * 100).toFixed(2);
        
        return validation;
    }

    /**
     * 计算模糊匹配分数
     * @param {Element} element 待检测元素
     * @param {Object} patterns 匹配模式
     * @returns {number} 模糊匹配分数
     */
    calculateFuzzyMatchScore(element, patterns) {
        let fuzzyScore = 0;

        // 收集元素的所有文本信息
        const elementTexts = [
            element.id || '',
            element.name || '',
            element.placeholder || '',
            element.className || ''
        ].filter(text => text.length > 0);

        const label = this.findAssociatedLabel(element);
        if (label) {
            elementTexts.push(label);
        }

        // 收集模式的所有文本信息
        const patternTexts = [
            ...(patterns.ids || []),
            ...(patterns.names || []),
            ...(patterns.placeholders || []),
            ...(patterns.labels || []),
            ...(patterns.classes || [])
        ];

        // 计算文本相似度
        for (const elementText of elementTexts) {
            for (const patternText of patternTexts) {
                const similarity = this.calculateStringSimilarity(
                    elementText.toLowerCase(),
                    patternText.toLowerCase()
                );

                if (similarity > 0.7) {
                    fuzzyScore += 2;
                } else if (similarity > 0.5) {
                    fuzzyScore += 1;
                }
            }
        }

        // 特殊规则：检查常见的字段名称变体
        const specialRules = this.applySpecialMatchingRules(element, patterns);
        fuzzyScore += specialRules;

        return Math.min(fuzzyScore, 3); // 限制模糊匹配的最大分数
    }

    /**
     * 计算字符串相似度
     * @param {string} str1 字符串1
     * @param {string} str2 字符串2
     * @returns {number} 相似度 (0-1)
     */
    calculateStringSimilarity(str1, str2) {
        if (str1 === str2) return 1;
        if (str1.length === 0 || str2.length === 0) return 0;

        // 使用编辑距离算法
        const matrix = [];
        const len1 = str1.length;
        const len2 = str2.length;

        for (let i = 0; i <= len1; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= len2; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                if (str1.charAt(i - 1) === str2.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        const maxLen = Math.max(len1, len2);
        return (maxLen - matrix[len1][len2]) / maxLen;
    }

    /**
     * 应用特殊匹配规则
     * @param {Element} element 待检测元素
     * @param {Object} patterns 匹配模式
     * @returns {number} 特殊规则分数
     */
    applySpecialMatchingRules(element, patterns) {
        let specialScore = 0;

        // 检查MDAC特定的字段名称模式
        const mdacPatterns = {
            passExpDte: ['passport', 'expiry', 'expire', '护照', '到期'],
            region: ['country', 'region', 'code', '国家', '地区', '代码'],
            arrDt: ['arrival', 'arrive', 'entry', '到达', '入境'],
            depDt: ['departure', 'depart', 'leave', 'exit', '离开', '出境']
        };

        const elementId = (element.id || '').toLowerCase();
        const elementName = (element.name || '').toLowerCase();
        const label = (this.findAssociatedLabel(element) || '').toLowerCase();

        for (const [mdacField, keywords] of Object.entries(mdacPatterns)) {
            for (const keyword of keywords) {
                if (elementId.includes(keyword) ||
                    elementName.includes(keyword) ||
                    label.includes(keyword)) {

                    // 检查这个元素是否匹配当前检测的字段类型
                    const patternKeywords = [
                        ...(patterns.ids || []),
                        ...(patterns.names || []),
                        ...(patterns.labels || [])
                    ].map(p => p.toLowerCase());

                    if (patternKeywords.some(pk => pk.includes(keyword))) {
                        specialScore += 1;
                        break;
                    }
                }
            }
        }

        return specialScore;
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormFieldDetector;
} else {
    window.FormFieldDetector = FormFieldDetector;
}
